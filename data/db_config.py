dbHost = "************"
dbPort = 8902
dbUser = "admin"
dbPassword = "123456"
dbName = "dfs://train_data"
tableName = 'future_ta_cr_1m'
tableFreq = '60s'

INFO_FEATURE_COLS = [
    'datatime', 'symbol', 'open', 'close', 'high', 'low', 'vol', 'val'
]

ALL_FEATURE_COLS = [
    'vwap', 'beta', 'sma_7', 'sma_9', 'sma_25', 'ema_7', 'ema_9',
    'ema_25', 'wma_7', 'wma_9', 'wma_25', 'dema_7', 'dema_9', 'dema_25',
    'tema_7', 'tema_9', 'tema_25', 'trima_7', 'trima_9', 'trima_25', 'kama_7',
    'kama_9', 'kama_25', 't3_7', 't3_9', 't3_25', 'ma_7', 'ma_9', 'ma_25',
    'rsi_7', 'rsi_9', 'rsi_25', 'bBands_1', 'bBands_2', 'bBands_3',
    'stochf_2', 'stoch_1', 'stoch_2', 'stochRsi_2', 'trix_30', 'correl_30',
    'linearreg_slope_15', 'linearreg_intercept_15', 'linearreg_angle_15',
    'linearreg_15', 'bop', 'cci_14', 'trange', 'plus_dm_14',
    'plus_di_14', 'minus_dm_14', 'minus_di_14', 'dx_15', 'adx_15', 'adxr_15',
    'cmo_15', 'macd_12', 'macd_26', 'macd_9', 'macdExt_12', 'macdExt_26',
    'macdExt_9', 'macdFix_9_1', 'macdFix_2', 'macdFix_3', 'midPrice_14',
    'midPoint_14', 'mom_10', 'roc_10', 'rocp_10', 'rocr_10', 'rocr100',
    'ppo_12_26', 'aroon_14_2', 'ultOsc_7_14_28', 'willr_14', 'ad', 'obv',
    'avgPrice', 'medPrice', 'typPrice', 'wclPrice', 'atr_14', 'natr_14',
    'highest_30', 'highest_60', 'highest_240', 'highest_480', 'highest_720',
    'highest_1440', 'lowest_30', 'lowest_60', 'lowest_240', 'lowest_480',
    'lowest_720', 'lowest_1440', 'open_cr', 'high_cr', 'low_cr',
    'avg_cr_dapan_30min', 'avg_win_ratio_5min', 'avg_cr_dapan_1h', 'close_index_diff',
    'avg_win_ratio_1h', 'avg_win_ratio_30min', 'avg_cr_dapan_15min','avg_win_ratio_15min',
    'funding_rate_min', 'avg_cr_dapan_5min', 'avg_cr_dapan_1min', 'avg_win_ratio_1min'
]

LABEL_COLS = [
    'open_cr_1', 'open_cr_3', 'open_cr_5', 'open_cr_10', 'open_cr_15',
    'open_cr_30', 'open_cr_60'
]

TIME_COL = 'datatime'
TICKER_COL = 'symbol'

LOCAL_DATA_DIR = 'TOP_5'


TI_TIMEFRAME = [1, 2, 3, 5, 10, 15, 20, 30, 40, 60, 240, 480, 720, 1440]
EMA_TIMEFRAME = [5, 15, 30, 60, 240]
RSI_TIMEFRAME = [5, 10, 30]
