import dolphindb as ddb
import numpy as np
import pandas as pd
import itertools
from datetime import datetime, timedelta
from loguru import logger
from data.db_config import *
import os
import fire

def get_dophinpd(sql) -> pd.DataFrame:
    s = ddb.session()
    s.connect(dbHost, dbPort, dbUser, dbPassword)
    table = s.run(sql, clearMemory = True)
    s.close()
    return table


def date_format(date_str):
    return datetime.strptime(date_str, '%Y.%m.%d').strftime('%Y-%m-%d')


def read_dataset_remote(symbol, start_date, end_date):
    # 读取config中的所有特征ALL_FEATURE_COLS，去重
    all_col = []
    for fea in itertools.chain(INFO_FEATURE_COLS, ALL_FEATURE_COLS, LABEL_COLS):
        if fea not in all_col: all_col.append(fea)
    all_col_sql = ",".join(all_col)
    sql = f'select {all_col_sql} from loadTable("{dbName}", "{tableName}") where symbol = "{symbol}" and ' + 'date(datatime) >= {st} and date(datatime) < {et} order by datatime'
    logger.debug(f'sql={sql}')
    table = get_dophinpd(sql.format(st=start_date,
                                    et=end_date))
    logger.info(f'raw table, len(table)={len(table)}')
    # 根据datatime去重
    table.drop_duplicates(subset='datatime', inplace=True)
    table.reset_index(drop=True, inplace=True)
    logger.debug(f'after drop_duplicates, len(table)={len(table)}')
    # ffill
    table = table.replace([np.inf, -np.inf], np.nan)
    table = table.ffill()
    logger.debug(f'after ffill, len(table)={len(table)}')
    # 按照策略时间处理+ffill
    start_date = date_format(start_date)
    end_date = date_format(end_date)
    date_range = pd.date_range(start_date, end_date, freq=tableFreq, closed='left')
    logger.info(f'index missing: {len(date_range) - len(table)}, date_range:{date_range[0]}~{date_range[-1]}')
    table['datatime'] = pd.to_datetime(table['datatime'])
    table.set_index('datatime', inplace=True)
    table_ = table.asfreq(tableFreq, method="ffill")
    fill_zero_table = table.asfreq(tableFreq, fill_value=0)
    table_[LABEL_COLS] = fill_zero_table[LABEL_COLS]
    logger.info(f'after asfreq+ffill, len(table_)={len(table_)}, table_.index:{table_.index[0]}~{table_.index[-1]}')
    return table_.reset_index()


def read_dataset_local(symbol, start_date, end_date):
    start_date = datetime.strptime(start_date, '%Y.%m.%d')
    end_date = datetime.strptime(end_date, '%Y.%m.%d')
    local_file = f'data_snap/{LOCAL_DATA_DIR}/{symbol.replace("/", "#")}'
    if os.path.exists(local_file):
        data = pd.read_parquet(local_file)
        data.set_index('datatime', inplace=True)
        if data.index[0] <= start_date and data.index[-1] >= end_date - timedelta(days=1):
            data = data.loc[start_date:end_date]
            logger.info(f'read local data, len(data)={len(data)}')
            logger.debug(f'data.index: {data.index[0]}~{data.index[-1]}')
            return data.reset_index()
    return None


def read_dataset(symbol, start_date, end_date, rewrite=False):
    data = None
    logger.info(f'read symbol={symbol}')
    if not rewrite:
        data = read_dataset_local(symbol, start_date, end_date)
    if data is None:
        data = read_dataset_remote(symbol, start_date, end_date)
        os.makedirs(f'data_snap/{LOCAL_DATA_DIR}', exist_ok=True)
        data.to_parquet(f'data_snap/{LOCAL_DATA_DIR}/{symbol.replace("/", "#")}')
    return data


def get_all_dataset(symbols, start_date='2020.10.01', end_date='2024.03.01', rewrite=False, return_data=False):
    N = len(symbols)
    for i, sym in enumerate(symbols):
        logger.info(f'[{i+1}/{N}], sym={sym}')
        dataset = read_dataset(sym, start_date, end_date, rewrite)
        logger.info(f'[{i+1}/{N}], sym={sym}, {dataset.shape}')


if __name__ == '__main__':
    fire.Fire()
    # ["ETH#USDT:USDT","BCH#USDT:USDT","BTC#USDT:USDT","XRP#USDT:USDT","LTC#USDT:USDT"]
    # get_all_dataset(['DOGE#USDT:USDT'], '2020.10.01', '2024.03.01', rewrite=True)