import numpy as np
import pandas as pd
import fire
import os
from loguru import logger
from data.dl_data import read_dataset
from data.db_config import TIME_COL, TICKER_COL, TI_TIMEFRAME, EMA_TIMEFRAME, RSI_TIMEFRAME


def add_tech_idx(df: pd.DataFrame):

    # add close EMA
    logger.info("add tech indicator")
    for i in EMA_TIMEFRAME:
        df[f"close_{i}_ema"] = df["close"].ewm(span=i, adjust=False).mean()


    # Returns and Trading Volume Changes
    logger.info("add Returns & Trading Volume Changes & Volatility")
    for i in TI_TIMEFRAME:

        df[f"r-{i}"] = df["close"].pct_change(i)      
        df[f"v-{i}"] = df["vol"].pct_change(i)

    # Volatility
    for i in TI_TIMEFRAME:
        if i >= 5:
            df[f'sig-{i}'] = np.log(1 + df["r-1"]).rolling(i).std()

    logger.info("add MACD & RSI & Boll")
    # Moving Average Convergence Divergence (MACD)
    df["macd_lmw"] = df["r-1"].ewm(span=26, adjust=False).mean()
    df["macd_smw"] = df["r-1"].ewm(span=12, adjust=False).mean()
    df["macd_bl"] = df["r-1"].ewm(span=9, adjust=False).mean()
    df["macd"] = df["macd_smw"] - df["macd_lmw"]

    # Relative Strength Indicator (RSI)
    for rsi_lb in RSI_TIMEFRAME:
        pos_gain = df["r-1"].where(df["r-1"] > 0, 0).ewm(rsi_lb).mean()
        neg_gain = df["r-1"].where(df["r-1"] < 0, 0).ewm(rsi_lb).mean()
        rs = np.abs(pos_gain/neg_gain)
        df[f"rsi-{rsi_lb}"] = 100 * rs/(1 + rs)

    # Bollinger Bands
    bollinger_lback = 10
    df["boll"] = df["r-1"].ewm(bollinger_lback).mean()
    df["boll_lb"] = df["boll"] - 2 * df["r-1"].rolling(bollinger_lback).std()
    df["boll_ub"] = df["boll"] + 2 * df["r-1"].rolling(bollinger_lback).std()
    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    # df.dropna(inplace=True)
    return df


def split_train_valid_test(df: pd.DataFrame,
                           train_valid_ratio,
                           start_date,
                           test_start_date,
                           test_end_date,
                           split_ticker=True):
    trainvalid = df.loc[(df[TIME_COL] > start_date) & (df[TIME_COL] < test_start_date)]
    test = df.loc[(df[TIME_COL] >= test_start_date) & (df[TIME_COL] < test_end_date)]
    if split_ticker:
        tickers = list(trainvalid[TICKER_COL].unique())
        train, valid = [], []
        for ticker in tickers:
            calib_data = trainvalid[trainvalid[TICKER_COL] == ticker]
            T = len(calib_data)
            train_valid_split = int(train_valid_ratio * T)
            train.append(calib_data.iloc[:train_valid_split, :].copy())
            valid.append(calib_data.iloc[train_valid_split:, :].copy())
        train = pd.concat(train)
        valid = pd.concat(valid)
    else:
        train_valid_split = int(train_valid_ratio * len(trainvalid))
        train = trainvalid.iloc[:train_valid_split, :].copy()
        valid = trainvalid.iloc[train_valid_split:, :].copy()
    return train, valid, test


def portfolio_features(symbols, cwd, train_valid_ratio=0.8, start_date='2020.10.01', test_start_data='2023.03.01', end_date='2024.03.01'):
    N = len(symbols)
    for i, sym in enumerate(symbols):
        logger.info(f'[{i+1}/{N}], {sym}')
        data = add_tech_idx(read_dataset(sym, start_date, end_date, rewrite=False))
        data = data.add_suffix(f'_{sym}')
        data = data.rename(columns={f'datatime_{sym}': 'datatime'})
        dataset = data if i == 0 else pd.merge(dataset, data, how='left', on='datatime')
    dataset.ffill(inplace=True)
    dataset.dropna(inplace=True)
    logger.info(f'len(dataset)={len(dataset)}')
    train, valid, test = split_train_valid_test(dataset,
                                                train_valid_ratio,
                                                start_date,
                                                test_start_data,
                                                end_date,
                                                split_ticker=False)
    os.makedirs(cwd, exist_ok=True)
    train.to_parquet(f'{cwd}/train.pq')
    valid.to_parquet(f'{cwd}/valid.pq')
    test.to_parquet(f'{cwd}/test.pq')


def cta_features(symbols, cwd, train_valid_ratio=0.8, start_date='2020.10.01', test_start_data='2023.03.01', end_date='2024.03.01'):

    dataset = pd.concat(
        [
            add_tech_idx(read_dataset(sym, start_date, end_date, rewrite=False))
            for sym in symbols
        ]
    )
    dataset.ffill(inplace=True)
    dataset.dropna(inplace=True)
    train, valid, test = split_train_valid_test(dataset,
                                                train_valid_ratio,
                                                start_date,
                                                test_start_data,
                                                end_date,
                                                split_ticker=True)
    os.makedirs(cwd, exist_ok=True)
    train.to_parquet(f'{cwd}/train.pq')
    valid.to_parquet(f'{cwd}/valid.pq')
    test.to_parquet(f'{cwd}/test.pq')



if __name__ == '__main__':
    fire.Fire()