import numpy as np
import torch as th
from torch import nn

from .AgentBase import AgentBase
from .AgentBase import build_mlp, layer_init_with_orthogonal
from ..train import Config
import torch.nn.functional as F

TEN = th.Tensor

activation = nn.Tanh

# orthogonal init
def orthogonal_init(layer: th.nn.Module, gain=1.0):
    if hasattr(layer, 'weight'):
        nn.init.orthogonal_(layer.weight, gain=gain)
    if hasattr(layer, 'bias'):
        nn.init.constant_(layer.bias, 0)


class AgentPPOMax(AgentBase):
    """PPO algorithm + GAE
    “Proximal Policy Optimization Algorithms”. <PERSON>. et al.. 2017.
    “Generalized Advantage Estimation”. <PERSON>. et al..
    """

    def __init__(self, net_dims: [int], state_dim: int, action_dim: int, gpu_id: int = 0, args: Config = Config()):
        super().__init__(net_dims, state_dim, action_dim, gpu_id, args)
        self.if_off_policy = False
        self.max_train_steps = args.break_step
        self.total_steps = 0
        self.policy_dist = getattr(args, 'policy_dist', 'gaussian')
        if self.policy_dist == 'beta':
            self.act = ActorPPOBeta(net_dims=net_dims, state_dim=state_dim, action_dim=action_dim).to(self.device)
        else:
            self.act = ActorPPO(net_dims=net_dims, state_dim=state_dim, action_dim=action_dim).to(self.device)

        self.cri = CriticPPO(net_dims=net_dims, state_dim=state_dim, action_dim=action_dim).to(self.device)
        self.act_optimizer = th.optim.Adam(self.act.parameters(), self.learning_rate, eps=1e-5)
        self.cri_optimizer = th.optim.Adam(self.cri.parameters(), self.learning_rate, eps=1e-5)
        self.norm_nums = getattr(args, "norm_nums", 0)
        self.ratio_clip = getattr(args, "ratio_clip", 0.2)  # `ratio.clamp(1 - clip, 1 + clip)`
        self.lambda_gae_adv = getattr(args, "lambda_gae_adv", 0.95)  # could be 0.80~0.99
        self.lambda_entropy = getattr(args, "lambda_entropy", 0.01)  # could be 0.00~0.10
        self.use_state_norm = getattr(args, "use_state_norm", True)
        # self.lambda_entropy = th.tensor(self.lambda_entropy, dtype=th.float32, device=self.device)

        self.if_use_v_trace = getattr(args, 'if_use_v_trace', True)

    def _explore_one_env(self, env, horizon_len: int, if_random: bool = False) -> tuple[TEN, TEN, TEN, TEN, TEN, TEN]:
        """
        Collect trajectories through the actor-environment interaction for a **single** environment instance.

        env: RL training environment. env.reset() env.step(). It should be a vector env.
        horizon_len: collect horizon_len step while exploring to update networks
        return: `(states, actions, logprobs, rewards, undones, unmasks)` for on-policy
            num_envs == 1
            `states.shape == (horizon_len, num_envs, state_dim)`
            `actions.shape == (horizon_len, num_envs, action_dim)`
            `logprobs.shape == (horizon_len, num_envs, action_dim)`
            `rewards.shape == (horizon_len, num_envs)`
            `undones.shape == (horizon_len, num_envs)`
            `unmasks.shape == (horizon_len, num_envs)`
        """
        states = th.zeros((horizon_len, self.state_dim), dtype=th.float32).to(self.device)
        actions = th.zeros((horizon_len, self.action_dim), dtype=th.float32).to(self.device) \
            if not self.if_discrete else th.zeros(horizon_len, dtype=th.int32).to(self.device)
        logprobs = th.zeros(horizon_len, dtype=th.float32).to(self.device)
        rewards = th.zeros(horizon_len, dtype=th.float32).to(self.device)
        terminals = th.zeros(horizon_len, dtype=th.bool).to(self.device)
        truncates = th.zeros(horizon_len, dtype=th.bool).to(self.device)

        state = self.last_state  # shape == (1, state_dim) for a single env.
        convert = self.act.convert_action_for_env
        for t in range(horizon_len):
            action, logprob = [t[0] for t in self.explore_action(state)]

            states[t] = state
            actions[t] = action
            logprobs[t] = logprob

            ary_action = convert(action).detach().cpu().numpy()
            ary_state, reward, terminal, truncate, _ = env.step(ary_action)
            if terminal or truncate:
                ary_state, info_dict = env.reset()
            state = th.as_tensor(ary_state, dtype=th.float32, device=self.device).unsqueeze(0)
            rewards[t] = float(reward)
            terminals[t] = terminal
            truncates[t] = truncate
        
        if self.use_state_norm:
            self.update_avg_std_for_normalization(states, self.norm_nums)
        # print(action, logprob, self.act.action_std_log)
        self.last_state = state  # state.shape == (1, state_dim) for a single env.
        '''add dim1=1 below for workers buffer_items concat'''
        # print(f'{self.act.state_avg=}, {self.act.state_std=}, {self.cri.state_avg=}, {self.cri.state_std=}')
        states = states.view((horizon_len, 1, self.state_dim))
        actions = actions.view((horizon_len, 1, self.action_dim)) \
            if not self.if_discrete else actions.view((horizon_len, 1))
        logprobs = logprobs.view((horizon_len, 1))
        rewards = (rewards * self.reward_scale).view((horizon_len, 1))
        # rewards = rewards.view((horizon_len, 1))
        undones = th.logical_not(terminals).view((horizon_len, 1))
        unmasks = th.logical_not(truncates).view((horizon_len, 1))
        return states, actions, logprobs, rewards, undones, unmasks

    def _explore_vec_env(self, env, horizon_len: int, if_random: bool = False) -> tuple[TEN, TEN, TEN, TEN, TEN, TEN]:
        """
        Collect trajectories through the actor-environment interaction for a **vectorized** environment instance.

        env: RL training environment. env.reset() env.step(). It should be a vector env.
        horizon_len: collect horizon_len step while exploring to update networks
        return: `(states, actions, logprobs, rewards, undones, unmasks)` for on-policy
            `states.shape == (horizon_len, num_envs, state_dim)`
            `actions.shape == (horizon_len, num_envs, action_dim)`
            `logprobs.shape == (horizon_len, num_envs, action_dim)`
            `rewards.shape == (horizon_len, num_envs)`
            `undones.shape == (horizon_len, num_envs)`
            `unmasks.shape == (horizon_len, num_envs)`
        """
        states = th.zeros((horizon_len, self.num_envs, self.state_dim), dtype=th.float32).to(self.device)
        actions = th.zeros((horizon_len, self.num_envs, self.action_dim), dtype=th.float32).to(self.device) \
            if not self.if_discrete else th.zeros((horizon_len, self.num_envs), dtype=th.int32).to(self.device)
        logprobs = th.zeros((horizon_len, self.num_envs), dtype=th.float32).to(self.device)
        rewards = th.zeros((horizon_len, self.num_envs), dtype=th.float32).to(self.device)
        terminals = th.zeros((horizon_len, self.num_envs), dtype=th.bool).to(self.device)
        truncates = th.zeros((horizon_len, self.num_envs), dtype=th.bool).to(self.device)

        state = self.last_state  # shape == (num_envs, state_dim) for a vectorized env.

        convert = self.act.convert_action_for_env
        for t in range(horizon_len):
            action, logprob = self.explore_action(state)

            states[t] = state
            actions[t] = action
            logprobs[t] = logprob

            state, reward, terminal, truncate, _ = env.step(convert(action))  # next_state

            rewards[t] = reward
            terminals[t] = terminal
            truncates[t] = truncate

        self.last_state = state
        rewards *= self.reward_scale
        undones = th.logical_not(terminals)
        unmasks = th.logical_not(truncates)
        return states, actions, logprobs, rewards, undones, unmasks

    def explore_action(self, state: TEN) -> tuple[TEN, TEN]:
        actions, logprobs = self.act.get_action(state)
        return actions, logprobs

    def update_net(self, buffer, num_steps) -> tuple[float, float, float]:
        buffer_size = buffer[0].shape[0]

        '''get advantages reward_sums'''
        with th.no_grad():
            states, actions, logprobs, rewards, undones, unmasks = buffer
            bs = max(1, 2 ** 10 // self.num_envs)  # set a smaller 'batch_size' to avoid CUDA OOM
            values = [self.cri(states[i:i + bs]) for i in range(0, buffer_size, bs)]
            values = th.cat(values, dim=0).squeeze(-1)  # values.shape == (buffer_size, )
            advantages = self.get_advantages(states, rewards, undones, unmasks, values)  # shape == (buffer_size, )
            reward_sums = advantages + values  # reward_sums.shape == (buffer_size, )
            y_pred, y_true = values.cpu().numpy(), rewards.cpu().numpy()
            var_y = np.var(y_true)
            explained_var = np.nan if var_y == 0 else 1 - np.var(y_true - y_pred) / var_y

            # print(f'{th.mean(values)=}, {th.std(values)=}, {th.mean(advantages)=}, {th.std(advantages)=}, {th.mean(reward_sums)=}, {th.std(reward_sums)=}')
            del rewards, undones, values

            # advantages = (advantages - advantages.mean()) / (advantages[::4, ::4].std() + 1e-5)  # avoid CUDA OOM
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-5)  # avoid CUDA OOM

            # self.update_avg_std_for_normalization(states, self.norm_nums)
            assert logprobs.shape == advantages.shape == reward_sums.shape == (buffer_size, states.shape[1])

        buffer = states, actions, unmasks, logprobs, advantages, reward_sums

        '''update network'''
        obj_entropies = []
        obj_critics = []
        obj_actors = []

        th.set_grad_enabled(True)
        update_times = int(buffer_size * self.repeat_times / self.batch_size)
        assert update_times >= 1
        clipfracs = []
        for update_t in range(update_times):
            obj_critic, obj_actor, obj_entropy, old_approx_kl, approx_kl, clipfrac = self.update_objectives(buffer, update_t)
            obj_entropies.append(obj_entropy)
            obj_critics.append(obj_critic)
            obj_actors.append(obj_actor)
            clipfracs.append(clipfrac)
        th.set_grad_enabled(False)

        self.total_steps += num_steps
        self.lr_decay(self.total_steps)

        # Regularize action_std_log to prevent it from becoming too small
        # self.regularize_action_std()
        # print(f"{self.act.state_avg=}, {self.act.state_std=}, {self.cri.state_avg=}, {self.cri.state_std=}")
        # print(f"{obj_critic=}, {obj_actor=}, {obj_entropy=}")
        # print(f'action_std_log: {self.act.action_std_log.data}, state_open: {np.sum(states.detach().cpu().numpy()>0.48)}, state_hold: {np.sum(states.detach().cpu().numpy()<=0.48)}, action_open: {np.sum(actions.detach().cpu().numpy()>0)}, action_hold: {np.sum(actions.detach().cpu().numpy()<=0)}')
        # for name, param in self.act.named_parameters():
        #     if param.requires_grad: print('act', name, param.data)
        # for name, param in self.cri.named_parameters():
        #     if param.requires_grad: print('cri', name, param.data)

        obj_entropy_avg = np.array(obj_entropies).mean() if len(obj_entropies) else 0.0
        obj_critic_avg = np.array(obj_critics).mean() if len(obj_critics) else 0.0
        obj_actor_avg = np.array(obj_actors).mean() if len(obj_actors) else 0.0

        return obj_critic_avg, obj_actor_avg, \
            {'value_loss': obj_critic_avg, 'policy_loss': obj_actor_avg, 'entropy': obj_entropy_avg, 'opt': self.act_optimizer,
             'clipfracs': clipfracs, 'old_approx_kl': old_approx_kl, 'approx_kl': approx_kl, 'explained_var': explained_var,
             'state_avg': self.act.state_avg, 'state_std': self.act.state_std
             }, obj_entropy_avg

    def update_objectives(self, buffer: tuple[TEN, ...], update_t: int) -> tuple[float, float, float]:
        states, actions, unmasks, logprobs, advantages, reward_sums = buffer

        sample_len = states.shape[0]
        num_seqs = states.shape[1]
        ids = th.randint(sample_len * num_seqs, size=(self.batch_size,), requires_grad=False, device=self.device)
        ids0 = th.fmod(ids, sample_len)  # ids % sample_len
        ids1 = th.div(ids, sample_len, rounding_mode='floor')  # ids // sample_len

        state = states[ids0, ids1]
        action = actions[ids0, ids1]
        unmask = unmasks[ids0, ids1]
        logprob = logprobs[ids0, ids1]
        advantage = advantages[ids0, ids1]
        reward_sum = reward_sums[ids0, ids1]

        value = self.cri(state).squeeze(1)  # critic network predicts the reward_sum (Q value) of state
        obj_critic = (self.criterion(value, reward_sum) * unmask).mean()
        self.optimizer_backward(self.cri_optimizer, obj_critic)

        new_logprob, entropy = self.act.get_logprob_entropy(state, action)
        logratio = new_logprob - logprob.detach()

        # Clamp logratio to prevent extreme values
        logratio = th.clamp(logratio, -20.0, 20.0)
        ratio = logratio.exp()

        # Check for NaN in ratio and replace with safe values
        if th.isnan(ratio).any():
            print(f"Warning: NaN detected in ratio. logratio range: [{logratio.min():.4f}, {logratio.max():.4f}]")
            ratio = th.where(th.isnan(ratio), th.ones_like(ratio), ratio)

        with th.no_grad():
            # calculate approx_kl http://joschu.net/blog/kl-approx.html
            old_approx_kl = (-logratio).mean()
            approx_kl = ((ratio - 1) - logratio).mean()
            clipfrac = ((ratio - 1.0).abs() > self.ratio_clip).float().mean().item()
        surrogate1 = advantage * ratio
        surrogate2 = advantage * ratio.clamp(1 - self.ratio_clip, 1 + self.ratio_clip)
        surrogate = th.min(surrogate1, surrogate2)  # save as below
        # surrogate = advantage * ratio * th.where(advantage.gt(0), 1 - self.ratio_clip, 1 + self.ratio_clip)

        obj_surrogate = (surrogate * unmask).mean()  # major actor objective
        obj_entropy = (entropy * unmask).mean()  # minor actor objective
        obj_actor_full = obj_surrogate + obj_entropy * self.lambda_entropy

        # Check for NaN in objectives before backward pass
        if th.isnan(obj_actor_full).any():
            print(f"Warning: NaN detected in obj_actor_full. Skipping backward pass.")
            obj_actor_full = th.tensor(0.0, device=obj_actor_full.device, requires_grad=True)

        self.optimizer_backward(self.act_optimizer, -obj_actor_full)
        # print('==============================')
        # grads = [(name, param.grad) for name, param in self.act.named_parameters()]
        # print('act grads:', grads)
        # grads = [(name, param.grad) for name, param in self.cri.named_parameters()]
        # print('cri grads:', grads)
        # for name, param in self.act.named_parameters():
        #     if param.requires_grad: print('act', name, param.data)
        # for name, param in self.cri.named_parameters():
        #     if param.requires_grad: print('cri', name, param.data)
        return obj_critic.item(), obj_surrogate.item(), obj_entropy.item(), old_approx_kl, approx_kl, clipfrac

    def get_advantages(self, states: TEN, rewards: TEN, undones: TEN, unmasks: TEN, values: TEN) -> TEN:
        advantages = th.empty_like(values)  # advantage value

        # update undones rewards when truncated
        truncated = th.logical_not(unmasks)
        if th.any(truncated):
            rewards[truncated] += self.cri(states[truncated]).squeeze(1).detach()
            undones[truncated] = False

        masks = undones * self.gamma
        horizon_len = rewards.shape[0]

        next_state = self.last_state.clone()
        next_value = self.cri(next_state).detach().squeeze(-1)

        advantage = th.zeros_like(next_value)  # last advantage value by GAE (Generalized Advantage Estimate)
        if self.if_use_v_trace:  # get advantage value in reverse time series (V-trace)
            for t in range(horizon_len - 1, -1, -1):
                next_value = rewards[t] + masks[t] * next_value
                advantages[t] = advantage = next_value - values[t] + masks[t] * self.lambda_gae_adv * advantage
                next_value = values[t]
        else:  # get advantage value using the estimated value of critic network
            for t in range(horizon_len - 1, -1, -1):
                advantages[t] = rewards[t] - values[t] + masks[t] * advantage
                advantage = values[t] + self.lambda_gae_adv * advantages[t]
        return advantages
        
    def lr_decay(self, total_steps):
        lr_now = self.learning_rate * (1 - total_steps / self.max_train_steps)
        lr_now = max(lr_now, 3e-5)

        for p in self.act_optimizer.param_groups:
            p['lr'] = lr_now
        for p in self.cri_optimizer.param_groups:
            p['lr'] = lr_now


    def update_avg_std_for_normalization(self, states: TEN, end_idx=None):
        tau = self.state_value_tau
        if tau == 0:
            return
        states = states.view((-1, self.state_dim))
        # state_avg = states.mean(dim=0, keepdim=True)
        # state_std = states.std(dim=0, keepdim=True)
        state_avg = states.mean(dim=0)
        state_std = states.std(dim=0)
        if end_idx:
            self.act.state_avg[:end_idx] = self.act.state_avg[:end_idx] * (1 - tau) + state_avg[:end_idx] * tau
            self.act.state_std[:end_idx] = (self.act.state_std[:end_idx] * (1 - tau) + state_std[:end_idx] * tau).clamp_min(1e-4)
            self.cri.state_avg[:] = self.act.state_avg
            self.cri.state_std[:] = self.act.state_std
        else:
            self.act.state_avg[:] = self.act.state_avg * (1 - tau) + state_avg * tau
            self.act.state_std[:] = (self.act.state_std * (1 - tau) + state_std * tau).clamp_min(1e-4)
            self.cri.state_avg[:] = self.act.state_avg
            self.cri.state_std[:] = self.act.state_std

        # self.act_target.state_avg[:] = self.act.state_avg
        # self.act_target.state_std[:] = self.act.state_std
        # self.cri_target.state_avg[:] = self.cri.state_avg
        # self.cri_target.state_std[:] = self.cri.state_std


'''network'''
class ActorPPOBeta(th.nn.Module):
    def __init__(self, net_dims: list[int], state_dim: int, action_dim: int):
        super().__init__()
        dims=[state_dim, *net_dims]
        self.activation = activation
        shared_layers = []
        for i in range(len(dims) - 1):
            shared_layers.append(nn.Linear(dims[i], dims[i + 1]))
            if self.activation:
                shared_layers.append(self.activation())
        self.shared_layers = nn.ModuleList(shared_layers)

        self.beta_layer = nn.Linear(dims[-1], action_dim)
        self.alpha_layer = nn.Linear(dims[-1], action_dim)

        for layer in self.shared_layers:
            orthogonal_init(layer)
        orthogonal_init(self.alpha_layer, gain=0.01)
        orthogonal_init(self.beta_layer, gain=0.01)

        self.ActionDist = th.distributions.Beta
        # TBD: delete
        self.state_avg = nn.Parameter(th.zeros((state_dim,)), requires_grad=False)
        self.state_std = nn.Parameter(th.ones((state_dim,)), requires_grad=False)
    
    def state_norm(self, state: TEN) -> TEN:
        return (state - self.state_avg) / (self.state_std + 1e-4)

    def forward(self, state: TEN) -> TEN:
        x = self.state_norm(state)
        for layer in self.shared_layers:
            x = layer(x)

        action_alpha = F.softplus(self.alpha_layer(x)) + 1.0  # ensure alpha > 0
        action_beta = F.softplus(self.beta_layer(x)) + 1.0  # ensure beta > 0

        action = action_alpha / (action_alpha + action_beta)

        return self.convert_action_for_env(action)

    def get_action(self, state: TEN) -> tuple[TEN, TEN]:  # for exploration
        x = self.state_norm(state)
        for layer in self.shared_layers:
            x = layer(x)

        action_alpha = F.softplus(self.alpha_layer(x)) + 1.0  # ensure alpha > 0
        action_beta = F.softplus(self.beta_layer(x)) + 1.0  # ensure beta > 0

        # Clamp alpha and beta to prevent numerical instability
        action_alpha = th.clamp(action_alpha, min=1.0, max=100.0)
        action_beta = th.clamp(action_beta, min=1.0, max=100.0)

        dist = self.ActionDist(action_alpha, action_beta)
        action = dist.sample()
        logprob = dist.log_prob(action).sum(1)

        # Check for NaN and replace with safe values if needed
        if th.isnan(logprob).any():
            print(f"Warning: NaN detected in Beta logprob during get_action.")
            logprob = th.where(th.isnan(logprob), th.zeros_like(logprob), logprob)

        return action, logprob

    def get_logprob_entropy(self, state: TEN, action: TEN) -> tuple[TEN, TEN]:
        x = self.state_norm(state)
        for layer in self.shared_layers:
            x = layer(x)

        action_alpha = F.softplus(self.alpha_layer(x)) + 1.0  # ensure alpha > 0
        action_beta = F.softplus(self.beta_layer(x)) + 1.0  # ensure beta > 0

        # Clamp alpha and beta to prevent numerical instability
        action_alpha = th.clamp(action_alpha, min=1.0, max=100.0)
        action_beta = th.clamp(action_beta, min=1.0, max=100.0)

        dist = self.ActionDist(action_alpha, action_beta)
        logprob = dist.log_prob(action).sum(1)
        entropy = dist.entropy().sum(1)

        # Check for NaN and replace with safe values if needed
        if th.isnan(logprob).any() or th.isnan(entropy).any():
            print(f"Warning: NaN detected in Beta get_logprob_entropy.")
            logprob = th.where(th.isnan(logprob), th.zeros_like(logprob), logprob)
            entropy = th.where(th.isnan(entropy), th.zeros_like(entropy), entropy)

        return logprob, entropy

    @staticmethod
    def convert_action_for_env(action: TEN) -> TEN:
        return action
    

class ActorPPO(th.nn.Module):
    def __init__(self, net_dims: list[int], state_dim: int, action_dim: int):
        super().__init__()
        self.activation = activation
        # self.net = build_mlp(dims=[state_dim, *net_dims, action_dim], activation=self.activation, if_raw_out=False)
        # for layer in self.net[:-2]:
        #     orthogonal_init(layer)
        # orthogonal_init(self.net[-2], gain=0.01)
        self.net = build_mlp(dims=[state_dim, *net_dims, action_dim], activation=self.activation)
        for layer in self.net[:-1]:
            orthogonal_init(layer)
        orthogonal_init(self.net[-1], gain=0.01)

        self.action_std_log = nn.Parameter(th.zeros((1, action_dim)), requires_grad=True)  # trainable parameter
        self.ActionDist = th.distributions.normal.Normal

        self.state_avg = nn.Parameter(th.zeros((state_dim,)), requires_grad=False)
        self.state_std = nn.Parameter(th.ones((state_dim,)), requires_grad=False)

    def state_norm(self, state: TEN) -> TEN:
        return (state - self.state_avg) / (self.state_std + 1e-4)

    def forward(self, state: TEN) -> TEN:
        state = self.state_norm(state)
        action = self.net(state)
        return action

    def get_action(self, state: TEN) -> tuple[TEN, TEN]:  # for exploration
        state = self.state_norm(state)
        action_avg = self.net(state)

        action_std = self.action_std_log.exp()

        dist = self.ActionDist(action_avg, action_std)
        action = dist.sample()
        logprob = dist.log_prob(action).sum(1)

        # Check for NaN and replace with safe values if needed
        if th.isnan(logprob).any():
            print(f"Warning: NaN detected in logprob during get_action. action_std_log: {self.action_std_log.data}")
            logprob = th.where(th.isnan(logprob), th.zeros_like(logprob), logprob)

        return action, logprob

    def get_logprob_entropy(self, state: TEN, action: TEN) -> tuple[TEN, TEN]:
        state = self.state_norm(state)
        action_avg = self.net(state)

        action_std = self.action_std_log.exp()

        dist = self.ActionDist(action_avg, action_std)
        logprob = dist.log_prob(action).sum(1)
        entropy = dist.entropy().sum(1)

        # Check for NaN and replace with safe values if needed
        if th.isnan(logprob).any() or th.isnan(entropy).any():
            print(f"Warning: NaN detected in get_logprob_entropy. action_std_log: {self.action_std_log.data}")
            logprob = th.where(th.isnan(logprob), th.zeros_like(logprob), logprob)
            entropy = th.where(th.isnan(entropy), th.zeros_like(entropy), entropy)

        return logprob, entropy

    @staticmethod
    def convert_action_for_env(action: TEN) -> TEN:
        return action


class CriticPPO(th.nn.Module):
    def __init__(self, net_dims: list[int], state_dim: int, action_dim: int):
        super().__init__()
        assert isinstance(action_dim, int)
        self.activation = activation
        self.net = build_mlp(dims=[state_dim, *net_dims, 1], activation=self.activation, if_raw_out=True)
        for layer in self.net:
            orthogonal_init(layer)

        self.state_avg = nn.Parameter(th.zeros((state_dim,)), requires_grad=False)
        self.state_std = nn.Parameter(th.ones((state_dim,)), requires_grad=False)

    def forward(self, state: TEN) -> TEN:
        state = self.state_norm(state)
        value = self.net(state)
        return value  # advantage value

    def state_norm(self, state: TEN) -> TEN:
        return (state - self.state_avg) / (self.state_std + 1e-4)
