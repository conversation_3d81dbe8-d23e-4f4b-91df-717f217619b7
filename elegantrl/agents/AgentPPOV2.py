import numpy as np
import torch as th
from torch import nn

from .AgentBase import AgentBase
from .AgentBase import build_mlp, layer_init_with_orthogonal
from ..train import Config

TEN = th.Tensor


class AgentPPOV2(AgentBase):
    """PPO algorithm + GAE
    “Proximal Policy Optimization Algorithms”. <PERSON>. et al.. 2017.
    “Generalized Advantage Estimation”. <PERSON>. et al..
    """

    def __init__(self, net_dims: [int], state_dim: int, action_dim: int, gpu_id: int = 0, args: Config = Config()):
        super().__init__(net_dims, state_dim, action_dim, gpu_id, args)
        self.if_off_policy = False
        self.act = ActorPPO(net_dims=net_dims, state_dim=state_dim, action_dim=action_dim).to(self.device)
        self.cri = CriticPPO(net_dims=net_dims, state_dim=state_dim, action_dim=action_dim).to(self.device)
        self.act_optimizer = th.optim.Adam(self.act.parameters(), self.learning_rate, eps=1e-5)
        self.cri_optimizer = th.optim.Adam(self.cri.parameters(), self.learning_rate, eps=1e-5)
        self.norm_nums = getattr(args, "norm_nums", 0)
        self.ratio_clip = getattr(args, "ratio_clip", 0.2)  # `ratio.clamp(1 - clip, 1 + clip)`
        self.lambda_gae_adv = getattr(args, "lambda_gae_adv", 0.95)  # could be 0.80~0.99
        self.lambda_entropy = getattr(args, "lambda_entropy", 0.001)  # could be 0.00~0.10
        self.lambda_entropy = th.tensor(self.lambda_entropy, dtype=th.float32, device=self.device)

        self.if_use_v_trace = getattr(args, 'if_use_v_trace', True)

    def _explore_one_env(self, env, horizon_len: int, if_random: bool = False) -> tuple[TEN, TEN, TEN, TEN, TEN, TEN]:
        """
        Collect trajectories through the actor-environment interaction for a **single** environment instance.

        env: RL training environment. env.reset() env.step(). It should be a vector env.
        horizon_len: collect horizon_len step while exploring to update networks
        return: `(states, actions, logprobs, rewards, undones, unmasks)` for on-policy
            num_envs == 1
            `states.shape == (horizon_len, num_envs, state_dim)`
            `actions.shape == (horizon_len, num_envs, action_dim)`
            `logprobs.shape == (horizon_len, num_envs, action_dim)`
            `rewards.shape == (horizon_len, num_envs)`
            `undones.shape == (horizon_len, num_envs)`
            `unmasks.shape == (horizon_len, num_envs)`
        """
        states = th.zeros((horizon_len, self.state_dim), dtype=th.float32).to(self.device)
        actions = th.zeros((horizon_len, self.action_dim), dtype=th.float32).to(self.device) \
            if not self.if_discrete else th.zeros(horizon_len, dtype=th.int32).to(self.device)
        logprobs = th.zeros(horizon_len, dtype=th.float32).to(self.device)
        rewards = th.zeros(horizon_len, dtype=th.float32).to(self.device)
        terminals = th.zeros(horizon_len, dtype=th.bool).to(self.device)
        truncates = th.zeros(horizon_len, dtype=th.bool).to(self.device)

        state = self.last_state  # shape == (1, state_dim) for a single env.
        convert = self.act.convert_action_for_env
        for t in range(horizon_len):
            action, logprob = [t[0] for t in self.explore_action(state)]

            states[t] = state
            actions[t] = action
            logprobs[t] = logprob

            ary_action = convert(action).detach().cpu().numpy()
            ary_state, reward, terminal, truncate, _ = env.step(ary_action)
            if terminal or truncate:
                ary_state, info_dict = env.reset()
            state = th.as_tensor(ary_state, dtype=th.float32, device=self.device).unsqueeze(0)
            rewards[t] = reward
            terminals[t] = terminal
            truncates[t] = truncate

        self.last_state = state  # state.shape == (1, state_dim) for a single env.
        '''add dim1=1 below for workers buffer_items concat'''
        # self.update_avg_std_for_normalization(states, self.norm_nums)
        # print(f'{self.act.state_avg=}, {self.act.state_std=}, {self.cri.state_avg=}, {self.cri.state_std=}')
        states = states.view((horizon_len, 1, self.state_dim))
        actions = actions.view((horizon_len, 1, self.action_dim)) \
            if not self.if_discrete else actions.view((horizon_len, 1))
        logprobs = logprobs.view((horizon_len, 1))
        rewards = (rewards * self.reward_scale).view((horizon_len, 1))
        # rewards = rewards.view((horizon_len, 1))
        undones = th.logical_not(terminals).view((horizon_len, 1))
        unmasks = th.logical_not(truncates).view((horizon_len, 1))
        return states, actions, logprobs, rewards, undones, unmasks

    def _explore_vec_env(self, env, horizon_len: int, if_random: bool = False) -> tuple[TEN, TEN, TEN, TEN, TEN, TEN]:
        """
        Collect trajectories through the actor-environment interaction for a **vectorized** environment instance.

        env: RL training environment. env.reset() env.step(). It should be a vector env.
        horizon_len: collect horizon_len step while exploring to update networks
        return: `(states, actions, logprobs, rewards, undones, unmasks)` for on-policy
            `states.shape == (horizon_len, num_envs, state_dim)`
            `actions.shape == (horizon_len, num_envs, action_dim)`
            `logprobs.shape == (horizon_len, num_envs, action_dim)`
            `rewards.shape == (horizon_len, num_envs)`
            `undones.shape == (horizon_len, num_envs)`
            `unmasks.shape == (horizon_len, num_envs)`
        """
        states = th.zeros((horizon_len, self.num_envs, self.state_dim), dtype=th.float32).to(self.device)
        actions = th.zeros((horizon_len, self.num_envs, self.action_dim), dtype=th.float32).to(self.device) \
            if not self.if_discrete else th.zeros((horizon_len, self.num_envs), dtype=th.int32).to(self.device)
        logprobs = th.zeros((horizon_len, self.num_envs), dtype=th.float32).to(self.device)
        rewards = th.zeros((horizon_len, self.num_envs), dtype=th.float32).to(self.device)
        terminals = th.zeros((horizon_len, self.num_envs), dtype=th.bool).to(self.device)
        truncates = th.zeros((horizon_len, self.num_envs), dtype=th.bool).to(self.device)

        state = self.last_state  # shape == (num_envs, state_dim) for a vectorized env.

        convert = self.act.convert_action_for_env
        for t in range(horizon_len):
            action, logprob = self.explore_action(state)

            states[t] = state
            actions[t] = action
            logprobs[t] = logprob

            state, reward, terminal, truncate, _ = env.step(convert(action))  # next_state

            rewards[t] = reward
            terminals[t] = terminal
            truncates[t] = truncate

        self.last_state = state
        rewards *= self.reward_scale
        undones = th.logical_not(terminals)
        unmasks = th.logical_not(truncates)
        return states, actions, logprobs, rewards, undones, unmasks

    def explore_action(self, state: TEN) -> tuple[TEN, TEN]:
        actions, logprobs = self.act.get_action(state)
        return actions, logprobs

    def update_net(self, buffer) -> tuple[float, float, float]:
        buffer_size = buffer[0].shape[0]

        '''get advantages reward_sums'''
        with th.no_grad():
            states, actions, logprobs, rewards, undones, unmasks = buffer
            bs = max(1, 2 ** 10 // self.num_envs)  # set a smaller 'batch_size' to avoid CUDA OOM
            values = [self.cri(states[i:i + bs]) for i in range(0, buffer_size, bs)]
            values = th.cat(values, dim=0).squeeze(-1)  # values.shape == (buffer_size, )
            advantages = self.get_advantages(states, rewards, undones, unmasks, values)  # shape == (buffer_size, )
            reward_sums = advantages + values  # reward_sums.shape == (buffer_size, )

            y_pred, y_true = values.cpu().numpy(), rewards.cpu().numpy()
            var_y = np.var(y_true)
            explained_var = np.nan if var_y == 0 else 1 - np.var(y_true - y_pred) / var_y

            # print(f'{th.mean(values)=}, {th.std(values)=}, {th.mean(advantages)=}, {th.std(advantages)=}, {th.mean(reward_sums)=}, {th.std(reward_sums)=}')
            del rewards, undones, values

            # advantages = (advantages - advantages.mean()) / (advantages[::4, ::4].std() + 1e-5)  # avoid CUDA OOM
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)  # avoid CUDA OOM
            # self.update_avg_std_for_normalization(states, self.norm_nums)
            assert logprobs.shape == advantages.shape == reward_sums.shape == (buffer_size, states.shape[1])

        buffer = states, actions, unmasks, logprobs, advantages, reward_sums

        '''update network'''
        obj_entropies = []
        obj_critics = []
        obj_actors = []

        th.set_grad_enabled(True)
        update_times = int(buffer_size * self.repeat_times / self.batch_size)
        assert update_times >= 1
        clipfracs = []
        for update_t in range(update_times):
            obj_critic, obj_actor, obj_entropy, old_approx_kl, approx_kl, clipfrac = self.update_objectives(buffer, update_t)
            obj_entropies.append(obj_entropy)
            obj_critics.append(obj_critic)
            obj_actors.append(obj_actor)
            clipfracs.append(clipfrac)
        th.set_grad_enabled(False)

        # print(f"{self.act.state_avg=}, {self.act.state_std=}, {self.cri.state_avg=}, {self.cri.state_std=}")
        # print(f"{obj_critic=}, {obj_actor=}, {obj_entropy=}")
        # print(f'action_std_log: {self.act.action_std_log.data}, state_open: {np.sum(states.detach().cpu().numpy()>0.48)}, state_hold: {np.sum(states.detach().cpu().numpy()<=0.48)}, action_open: {np.sum(actions.detach().cpu().numpy()>0)}, action_hold: {np.sum(actions.detach().cpu().numpy()<=0)}')
        # for name, param in self.act.named_parameters():
        #     if param.requires_grad: print('act', name, param.data)
        # for name, param in self.cri.named_parameters():
        #     if param.requires_grad: print('cri', name, param.data)

        obj_entropy_avg = np.array(obj_entropies).mean() if len(obj_entropies) else 0.0
        obj_critic_avg = np.array(obj_critics).mean() if len(obj_critics) else 0.0
        obj_actor_avg = np.array(obj_actors).mean() if len(obj_actors) else 0.0

        return obj_critic_avg, obj_actor_avg, \
            {'value_loss': obj_critic_avg, 'policy_loss': obj_actor_avg, 'entropy': obj_entropy_avg, 'opt': self.act_optimizer,
             'clipfracs': clipfracs, 'old_approx_kl': old_approx_kl, 'approx_kl': approx_kl, 'explained_var': explained_var,
             'state_avg': self.act.state_avg, 'state_std': self.act.state_std
             }, obj_entropy_avg

    def update_objectives(self, buffer: tuple[TEN, ...], update_t: int) -> tuple[float, float, float]:
        states, actions, unmasks, logprobs, advantages, reward_sums = buffer

        sample_len = states.shape[0]
        num_seqs = states.shape[1]
        ids = th.randint(sample_len * num_seqs, size=(self.batch_size,), requires_grad=False, device=self.device)
        ids0 = th.fmod(ids, sample_len)  # ids % sample_len
        ids1 = th.div(ids, sample_len, rounding_mode='floor')  # ids // sample_len

        state = states[ids0, ids1]
        action = actions[ids0, ids1]
        unmask = unmasks[ids0, ids1]
        logprob = logprobs[ids0, ids1]
        advantage = advantages[ids0, ids1]
        reward_sum = reward_sums[ids0, ids1]

        value = self.cri(state).squeeze(1)  # critic network predicts the reward_sum (Q value) of state
        obj_critic = (self.criterion(value, reward_sum) * unmask).mean()
        self.optimizer_backward(self.cri_optimizer, obj_critic)

        new_logprob, entropy = self.act.get_logprob_entropy(state, action)
        logratio = new_logprob - logprob.detach()
        ratio = logratio.exp()
        with th.no_grad():
            # calculate approx_kl http://joschu.net/blog/kl-approx.html
            old_approx_kl = (-logratio).mean()
            approx_kl = ((ratio - 1) - logratio).mean()
            clipfrac = ((ratio - 1.0).abs() > self.ratio_clip).float().mean().item()
        surrogate1 = advantage * ratio
        surrogate2 = advantage * ratio.clamp(1 - self.ratio_clip, 1 + self.ratio_clip)
        surrogate = th.min(surrogate1, surrogate2)  # save as below
        # surrogate = advantage * ratio * th.where(advantage.gt(0), 1 - self.ratio_clip, 1 + self.ratio_clip)

        obj_surrogate = (surrogate * unmask).mean()  # major actor objective
        obj_entropy = (entropy * unmask).mean()  # minor actor objective
        obj_actor_full = obj_surrogate + obj_entropy * self.lambda_entropy
        self.optimizer_backward(self.act_optimizer, -obj_actor_full)
        # print('==============================')
        # grads = [(name, param.grad) for name, param in self.act.named_parameters()]
        # print('act grads:', grads)
        # grads = [(name, param.grad) for name, param in self.cri.named_parameters()]
        # print('cri grads:', grads)
        # for name, param in self.act.named_parameters():
        #     if param.requires_grad: print('act', name, param.data)
        # for name, param in self.cri.named_parameters():
        #     if param.requires_grad: print('cri', name, param.data)
        return obj_critic.item(), obj_surrogate.item(), obj_entropy.item(), old_approx_kl, approx_kl, clipfrac

    def get_advantages(self, states: TEN, rewards: TEN, undones: TEN, unmasks: TEN, values: TEN) -> TEN:
        advantages = th.empty_like(values)  # advantage value

        # update undones rewards when truncated
        truncated = th.logical_not(unmasks)
        if th.any(truncated):
            rewards[truncated] += self.cri(states[truncated]).squeeze(1).detach()
            undones[truncated] = False

        masks = undones * self.gamma
        horizon_len = rewards.shape[0]

        next_state = self.last_state.clone()
        next_value = self.cri(next_state).detach().squeeze(-1)

        advantage = th.zeros_like(next_value)  # last advantage value by GAE (Generalized Advantage Estimate)
        if self.if_use_v_trace:  # get advantage value in reverse time series (V-trace)
            for t in range(horizon_len - 1, -1, -1):
                next_value = rewards[t] + masks[t] * next_value
                advantages[t] = advantage = next_value - values[t] + masks[t] * self.lambda_gae_adv * advantage
                next_value = values[t]
        else:  # get advantage value using the estimated value of critic network
            for t in range(horizon_len - 1, -1, -1):
                advantages[t] = rewards[t] - values[t] + masks[t] * advantage
                advantage = values[t] + self.lambda_gae_adv * advantages[t]
        return advantages

    def update_avg_std_for_normalization(self, states: TEN, end_idx=None):
        tau = self.state_value_tau
        if tau == 0:
            return
        states = states.view((-1, self.state_dim))
        # state_avg = states.mean(dim=0, keepdim=True)
        # state_std = states.std(dim=0, keepdim=True)
        state_avg = states.mean(dim=0)
        state_std = states.std(dim=0)
        if end_idx:
            self.act.state_avg[:end_idx] = self.act.state_avg[:end_idx] * (1 - tau) + state_avg[:end_idx] * tau
            self.act.state_std[:end_idx] = (self.act.state_std[:end_idx] * (1 - tau) + state_std[:end_idx] * tau).clamp_min(1e-4)
            self.cri.state_avg[:] = self.act.state_avg
            self.cri.state_std[:] = self.act.state_std
        else:
            self.act.state_avg[:] = self.act.state_avg * (1 - tau) + state_avg * tau
            self.act.state_std[:] = (self.act.state_std * (1 - tau) + state_std * tau).clamp_min(1e-4)
            self.cri.state_avg[:] = self.act.state_avg
            self.cri.state_std[:] = self.act.state_std

        # self.act_target.state_avg[:] = self.act.state_avg
        # self.act_target.state_std[:] = self.act.state_std
        # self.cri_target.state_avg[:] = self.cri.state_avg
        # self.cri_target.state_std[:] = self.cri.state_std


class AgentA2C(AgentPPOV2):
    """A2C algorithm.
    “Asynchronous Methods for Deep Reinforcement Learning”. 2016.
    """

    def update_net(self, buffer) -> tuple[float, float, float]:
        buffer_size = buffer[0].shape[0]

        '''get advantages reward_sums'''
        with th.no_grad():
            states, actions, logprobs, rewards, undones, unmasks = buffer
            bs = max(1, 2 ** 10 // self.num_envs)  # set a smaller 'batch_size' to avoid CUDA OOM
            values = [self.cri(states[i:i + bs]) for i in range(0, buffer_size, bs)]
            values = th.cat(values, dim=0).squeeze(-1)  # values.shape == (buffer_size, )

            advantages = self.get_advantages(states, rewards, undones, unmasks, values)  # shape == (buffer_size, )
            reward_sums = advantages + values  # reward_sums.shape == (buffer_size, )
            del rewards, undones, values

            advantages = (advantages - advantages.mean()) / (advantages[::4, ::4].std() + 1e-5)  # avoid CUDA OOM
            assert logprobs.shape == advantages.shape == reward_sums.shape == (buffer_size, states.shape[1])
        buffer = states, actions, unmasks, logprobs, advantages, reward_sums

        '''update network'''
        obj_critics = []
        obj_actors = []

        th.set_grad_enabled(True)
        update_times = int(buffer_size * self.repeat_times / self.batch_size)
        assert update_times >= 1
        for update_t in range(update_times):
            obj_critic, obj_actor = self.update_objectives(buffer, update_t)
            obj_critics.append(obj_critic)
            obj_actors.append(obj_actor)
        th.set_grad_enabled(False)

        obj_critic_avg = np.array(obj_critics).mean() if len(obj_critics) else 0.0
        obj_actor_avg = np.array(obj_actors).mean() if len(obj_actors) else 0.0
        return obj_critic_avg, obj_actor_avg, 0

    def update_objectives(self, buffer: tuple[TEN, ...], update_t: int) -> tuple[float, float]:
        states, actions, unmasks, logprobs, advantages, reward_sums = buffer

        buffer_size = states.shape[0]
        indices = th.randint(buffer_size, size=(self.batch_size,), requires_grad=False)
        state = states[indices]
        action = actions[indices]
        unmask = unmasks[indices]
        # logprob = logprobs[indices]
        advantage = advantages[indices]
        reward_sum = reward_sums[indices]

        value = self.cri(state).squeeze(1)  # critic network predicts the reward_sum (Q value) of state
        obj_critic = (self.criterion(value, reward_sum) * unmask).mean()
        self.optimizer_backward(self.cri_optimizer, obj_critic)

        new_logprob, obj_entropy = self.act.get_logprob_entropy(state, action)
        obj_actor = (advantage * new_logprob).mean()  # obj_actor without policy gradient clip
        self.optimizer_backward(self.act_optimizer, -obj_actor)
        return obj_critic.item(), obj_actor.item()


class AgentDiscretePPO(AgentPPOV2):
    def __init__(self, net_dims: [int], state_dim: int, action_dim: int, gpu_id: int = 0, args: Config = Config()):
        AgentPPOV2.__init__(self, net_dims, state_dim, action_dim, gpu_id, args)
        self.if_off_policy = False

        self.act = ActorDiscretePPO(net_dims=net_dims, state_dim=state_dim, action_dim=action_dim).to(self.device)
        self.cri = CriticPPO(net_dims=net_dims, state_dim=state_dim, action_dim=action_dim).to(self.device)
        self.act_optimizer = th.optim.Adam(self.act.parameters(), self.learning_rate)
        self.cri_optimizer = th.optim.Adam(self.cri.parameters(), self.learning_rate)

        self.ratio_clip = getattr(args, "ratio_clip", 0.25)  # `ratio.clamp(1 - clip, 1 + clip)`
        self.lambda_gae_adv = getattr(args, "lambda_gae_adv", 0.95)  # could be 0.80~0.99
        self.lambda_entropy = getattr(args, "lambda_entropy", 0.01)  # could be 0.00~0.10
        self.lambda_entropy = th.tensor(self.lambda_entropy, dtype=th.float32, device=self.device)

        self.if_use_v_trace = getattr(args, 'if_use_v_trace', True)


class AgentDiscreteA2C(AgentDiscretePPO):
    def __init__(self, net_dims: [int], state_dim: int, action_dim: int, gpu_id: int = 0, args: Config = Config()):
        AgentDiscretePPO.__init__(self, net_dims, state_dim, action_dim, gpu_id, args)
        self.if_off_policy = False

        self.act = ActorDiscretePPO(net_dims=net_dims, state_dim=state_dim, action_dim=action_dim).to(self.device)
        self.cri = CriticPPO(net_dims=net_dims, state_dim=state_dim, action_dim=action_dim).to(self.device)
        self.act_optimizer = th.optim.Adam(self.act.parameters(), self.learning_rate)
        self.cri_optimizer = th.optim.Adam(self.cri.parameters(), self.learning_rate)

        self.if_use_v_trace = getattr(args, 'if_use_v_trace', True)


'''network'''
import torch.nn.functional as F
class ActorPPOBeta(th.nn.Module):
    def __init__(self, net_dims: list[int], state_dim: int, action_dim: int):
        super().__init__()
        self.net = build_mlp(dims=[state_dim, *net_dims, action_dim])
        
        dims=[state_dim, *net_dims]
        activation = nn.GELU
        net_list = []
        for i in range(len(dims) - 1):
            net_list.extend([nn.Linear(dims[i], dims[i + 1]), activation()])
        
        self.beta_net = nn.Sequential(*net_list+[nn.Linear(dims[-1], action_dim)])
        self.alpha_net = nn.Sequential(*net_list+[nn.Linear(dims[-1], action_dim)])

        # layer_init_with_orthogonal(self.net[-1], std=0.1)
        layer_init_with_orthogonal(self.beta_net[-1], std=0.1)
        layer_init_with_orthogonal(self.alpha_net[-1], std=0.1)

        # th.nn.init.constant_(self.net[-1].weight, 1.0)
        # th.nn.init.constant_(self.net[-1].bias, 0.1)

        # self.action_std_log = nn.Parameter(th.zeros((1, action_dim)), requires_grad=True)  # trainable parameter
        # self.ActionDist = th.distributions.normal.Normal
        self.ActionDist = th.distributions.Beta

        self.state_avg = nn.Parameter(th.zeros((state_dim,)), requires_grad=False)
        self.state_std = nn.Parameter(th.ones((state_dim,)), requires_grad=False)

    def state_norm(self, state: TEN) -> TEN:
        return (state - self.state_avg) / (self.state_std + 1e-4)

    def forward(self, state: TEN) -> TEN:
        state = self.state_norm(state)
        action_alpha = F.softplus(self.alpha_net(state)) + 1  # ensure alpha > 0
        action_beta = F.softplus(self.beta_net(state)) + 1  # ensure beta > 0

        dist = self.ActionDist(action_alpha, action_beta)
        action = dist.mean()
        return self.convert_action_for_env(action)

    def get_action(self, state: TEN) -> tuple[TEN, TEN]:  # for exploration
        state = self.state_norm(state)
        action_alpha = F.softplus(self.alpha_net(state)) + 1  # ensure alpha > 0
        action_beta = F.softplus(self.beta_net(state)) + 1  # ensure beta > 0

        dist = self.ActionDist(action_alpha, action_beta)
        action = dist.sample()
        logprob = dist.log_prob(action).sum(1)
        return action, logprob

    def get_logprob_entropy(self, state: TEN, action: TEN) -> tuple[TEN, TEN]:
        state = self.state_norm(state)
        action_alpha = F.softplus(self.alpha_net(state)) + 1  # ensure alpha > 0
        action_beta = F.softplus(self.beta_net(state)) + 1  # ensure beta > 0

        dist = self.ActionDist(action_alpha, action_beta)
        logprob = dist.log_prob(action).sum(1)
        entropy = dist.entropy().sum(1)
        return logprob, entropy

    @staticmethod
    def convert_action_for_env(action: TEN) -> TEN:
        return action.tanh()
    

class ActorPPO(th.nn.Module):
    def __init__(self, net_dims: list[int], state_dim: int, action_dim: int):
        super().__init__()
        self.net = build_mlp(dims=[state_dim, *net_dims, action_dim])#, activation=th.nn.Tanh)
        layer_init_with_orthogonal(self.net[-1], std=0.01)

        # th.nn.init.constant_(self.net[-1].weight, 1.0)
        # th.nn.init.constant_(self.net[-1].bias, 0.1)

        self.action_std_log = nn.Parameter(th.zeros((1, action_dim)), requires_grad=True)  # trainable parameter
        self.ActionDist = th.distributions.normal.Normal

        self.state_avg = nn.Parameter(th.zeros((state_dim,)), requires_grad=False)
        self.state_std = nn.Parameter(th.ones((state_dim,)), requires_grad=False)

    def state_norm(self, state: TEN) -> TEN:
        return (state - self.state_avg) / (self.state_std + 1e-4)

    def forward(self, state: TEN) -> TEN:
        state = self.state_norm(state)
        action = self.net(state)
        return self.convert_action_for_env(action)

    def get_action(self, state: TEN) -> tuple[TEN, TEN]:  # for exploration
        state = self.state_norm(state)
        action_avg = self.net(state)
        action_std = self.action_std_log.exp()

        dist = self.ActionDist(action_avg, action_std)
        action = dist.sample()
        logprob = dist.log_prob(action).sum(1)
        return action, logprob

    def get_logprob_entropy(self, state: TEN, action: TEN) -> tuple[TEN, TEN]:
        state = self.state_norm(state)
        action_avg = self.net(state)
        action_std = self.action_std_log.exp()

        dist = self.ActionDist(action_avg, action_std)
        logprob = dist.log_prob(action).sum(1)
        entropy = dist.entropy().sum(1)
        return logprob, entropy

    @staticmethod
    def convert_action_for_env(action: TEN) -> TEN:
        return action.tanh()


class ActorDiscretePPO(ActorPPO):
    def __init__(self, net_dims: list[int], state_dim: int, action_dim: int):
        super().__init__(net_dims=net_dims, state_dim=state_dim, action_dim=action_dim)
        self.ActionDist = th.distributions.Categorical
        self.soft_max = nn.Softmax(dim=-1)

    def forward(self, state: TEN) -> TEN:
        state = self.state_norm(state)
        a_prob = self.net(state)  # action_prob without softmax
        return a_prob.argmax(dim=1)  # get the indices of discrete action

    def get_action(self, state: TEN) -> (TEN, TEN):
        state = self.state_norm(state)
        a_prob = self.soft_max(self.net(state))
        a_dist = self.ActionDist(a_prob)
        action = a_dist.sample()
        logprob = a_dist.log_prob(action)
        return action, logprob

    def get_logprob_entropy(self, state: TEN, action: TEN) -> (TEN, TEN):
        state = self.state_norm(state)
        a_prob = self.soft_max(self.net(state))  # action.shape == (batch_size, 1), action.dtype = th.int
        dist = self.ActionDist(a_prob)
        logprob = dist.log_prob(action)
        entropy = dist.entropy()
        return logprob, entropy

    @staticmethod
    def convert_action_for_env(action: TEN) -> TEN:
        return action.long()


class CriticPPO(th.nn.Module):
    def __init__(self, net_dims: list[int], state_dim: int, action_dim: int):
        super().__init__()
        assert isinstance(action_dim, int)
        self.net = build_mlp(dims=[state_dim, *net_dims, 1])
        layer_init_with_orthogonal(self.net[-1], std=0.5)

        self.state_avg = nn.Parameter(th.zeros((state_dim,)), requires_grad=False)
        self.state_std = nn.Parameter(th.ones((state_dim,)), requires_grad=False)

    def forward(self, state: TEN) -> TEN:
        state = self.state_norm(state)
        value = self.net(state)
        return value  # advantage value

    def state_norm(self, state: TEN) -> TEN:
        return (state - self.state_avg) / (self.state_std + 1e-4)
