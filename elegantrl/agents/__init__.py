from .AgentBase import AgentBase

# DQN (off-policy)
from .AgentDQN import <PERSON><PERSON><PERSON><PERSON>, AgentDuelingDQN
from .AgentDQN import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>D3Q<PERSON>
from .AgentEmbedDQN import <PERSON><PERSON>mbedDQ<PERSON>, AgentEnsembleDQN

# off-policy
from .AgentTD3 import AgentTD3, <PERSON><PERSON><PERSON><PERSON>
from .AgentSAC import <PERSON><PERSON><PERSON>, AgentModSAC

# on-policy
from .AgentPPO import <PERSON><PERSON><PERSON>, AgentDiscretePPO
from .AgentPPOV2 import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .AgentPPOMax import <PERSON><PERSON><PERSON><PERSON>

from .AgentPPO import AgentA2C, AgentDiscreteA2C
