import numpy as np
import pandas as pd
import os
from loguru import logger
from functorch import vmap

# CTA V1: 0425
class CTAEnvV1:

    env_name = 'CTAEnv-V1'
    olhcv_cols = ['open', 'close', 'low', 'high', 'vol', 'val']
    cwd = f'data_snap/portfolio_v1'
    feature_columns = [
                'open_max', 'close_max', 'low_max', 'high_max', 'vol_max', 'val_max', 'open_last', 'close_last', 'low_last', 'high_last',
                # MA
                'sma_7', 'sma_9', 'sma_25', 'ema_7', 'ema_9', 'ema_25', 'wma_7', 'wma_9', 'wma_25', 'dema_7', 'dema_9', 'dema_25',
                'tema_7', 'tema_9', 'tema_25', 'trima_7', 'trima_9', 'trima_25', 'kama_7', 'kama_9', 'kama_25',
                'rsi_7', 'rsi_9',  # 相对强弱指标
                'bBands_1', 'bBands_2', 'bBands_3', # 布林带
                'stochf_2', 'stoch_1', 'stoch_2', 'stochRsi_2', # Stochastic
                't3_7', 't3_9', 't3_25',    # T3
                'linearreg_slope_15', 'linearreg_intercept_15', 'linearreg_angle_15', 'linearreg_15',  # 线性回归
                'trange', 'midPrice_14', 'aroon_14_2', 'willr_14', 'ad', 'obv', 'avgPrice', 'medPrice', 'typPrice', 'natr_14',      # 其他指标
                'pre_cr_1', 'pre_cr_3', 'pre_cr_5', 'pre_cr_15', 'pre_cr_30', 'pre_cr_60', 'pre_cr_120', 'pre_cr_240', 'pre_cr_480', 'pre_cr_720', 'pre_cr_1440', 'pre_cr_10080', 'pre_cr_43200',   # 前向收益率
                'pre_vol_cr_1', 'pre_vol_cr_3', 'pre_vol_cr_5', 'pre_vol_cr_15', 'pre_vol_cr_30', 'pre_vol_cr_60', 'pre_vol_cr_240', 'pre_vol_cr_480', 'pre_vol_cr_720', 'pre_vol_cr_1440',     # 前向成交量收益率
                'avg_cr_dapan_30min', 'avg_win_ratio_30min', 'avg_cr_dapan_5min', 'avg_win_ratio_5min', 'avg_cr_dapan_1h', 'avg_win_ratio_1h', 'avg_cr_dapan_15min','avg_win_ratio_15min', 'avg_cr_dapan_1min', 'avg_win_ratio_1min', 'close_index_diff', 'funding_rate_min', # 大盘
    ]
    label_columns = [
            'open_cr_1', 'open_cr_3', 'open_cr_5', 'open_cr_10', 'open_cr_15',
            'open_cr_30', 'open_cr_60'
    ]

    if_discrete = False
    action_dim = 1
    pos_num = 1 # state中记录持有量
    # info = (norm_usd_capital, norm_crypto_capital, crypto_to_capital_ratio) 
    info_dim = 3

    def __init__(self, env_params):

        self.symbols = self._load_symbols()

        self.train_start_date = env_params.get('train_start_date', "2020.10.01")
        self.valid_start_date = env_params.get('valid_start_date', "2022.10.01")
        self.test_start_date = env_params.get('test_start_date', "2023.03.01")

        self.initial_asset = env_params.get('initial_asset', 1e6)
        self.transaction_cost = env_params.get('transaction_cost', 3e-4)
        self.target_label_col = env_params.get('target_label_col', 'open_cr_15')
        self.forward_kl_nums = int(self.target_label_col.split('_')[-1])
        self.max_open_capital = env_params.get('max_open_capital', self.initial_asset//2)       # 最大open
        self.min_open_capital = env_params.get('min_open_capital', self.initial_asset//100)      # 最小open

        self.max_close_capital = self.max_open_capital       # 最大close
        self.min_close_capital = env_params.get('min_close_capital', self.initial_asset//100)      # 最小close

        self.max_holding_capital = env_params.get('max_holding_capital', self.initial_asset)    # 最大hold
        self.capital_thresh = env_params.get('capital_thresh', 0.3)     # 警戒线
        self.stop_loss = env_params.get('stop_loss', 0.1)

        self.seq_len = env_params.get('seq_len', 60)
        self.fea_freqs = env_params.get('fea_freqs', 1)
    
        self.gamma = env_params.get('gamma', 0.999)
        self.random_reset = env_params.get('random_reset', True)
        self.min_kl_nums = env_params.get('min_kl_nums', 1e4)       # 最少KL样本
        self.reward_scale = env_params.get('reward_scale', 2**-8)
        self.shares_scale = env_params.get('shares_scale', 2**-3)
        self.neg_reward_scale = env_params.get('neg_reward_scale', 1)
        self.target_return = env_params.get('target_return', 100)

        # (state, pos, norm_usd_capital, norm_crypto_capital, crypto_to_capital_ratio)
        self.fea_dim = len(self.feature_columns) * self.seq_len
        self.state_dim = self.fea_dim + self.info_dim
        self.last_kl_offset = self.fea_freqs * (self.seq_len - 1)
        self.max_len = self.seq_len * self.fea_freqs

        self.reset(is_return=False)

    def _load_symbols(self):
        if os.path.exists(self.cwd):
            symbols = os.listdir(self.cwd)
            logger.info(f'load {len(symbols)} symbols from {self.cwd}')
        else:

            logger.error('cwd not exist...')
            exit(-1)
        return symbols
    

    def _load_data(self, symbol):
        logger.info(f'load data {symbol}')
        v = pd.read_parquet(f'{self.cwd}/{symbol}')
        for col in self.olhcv_cols:
            if col + '_max' in self.feature_columns:
                v[col + '_max'] = v[col]
            if col + '_last' in self.feature_columns:
                v[col + '_last'] = v[col]
        v = v.replace([np.inf, -np.inf], np.nan)
        v.set_index('datatime', inplace=True)
        v[self.feature_columns] = v[self.feature_columns].ffill()
        v[self.label_columns] = v[self.label_columns].fillna(0)
        v_ = v.asfreq('60s', method="ffill")
        fill_zero_table = v.asfreq('60s', fill_value=0)
        v_[self.label_columns] = fill_zero_table[self.label_columns]
        return v_.reset_index()


    def reset(self, is_return=True):
        symbol = np.random.choice(self.symbols, 1)
        self.data = self._load_data(symbol[0])
        self.start_kl = 0

        self.max_step = len(self.data) - self.forward_kl_nums - self.seq_len * self.fea_freqs - 1
        self.cash = self.initial_asset
        self.shares = 0
        self.init_close = self.data.iloc[self.last_kl_offset]['close']
        
        if self.random_reset:
            if self.max_step - self.min_kl_nums > 0:
                self.start_kl = np.random.randint(0, self.max_step - self.min_kl_nums)
            self.data = self.data.iloc[self.start_kl:]
            self.init_close = self.data.iloc[self.last_kl_offset]['close']
            self.cash = self.initial_asset * np.random.uniform(0.95, 1.05)
            self.shares = np.random.uniform(self.min_open_capital/self.init_close, self.max_holding_capital/self.init_close/10)
        
        self.cur_kl = 0
        self.cur_ts = self.data.iloc[self.cur_kl+self.last_kl_offset]['datatime']
        self.cur_action = 0
        self.prev_action = 0
        self.gamma_return = 0.0
        self.cumulative_returns = 0.0
        self.reward_offset = 0.0
        self.episode_orders = 0
        self.prev_episode_orders = 0
        self.reward_list = []
        self.raw_reward_list = []

        self.cur_close = self.init_close
        self.total_asset = np.sum(self.cur_close * self.shares) + self.cash
        self.next_total_asset = 0
        logger.info(f'reset, start: {self.start_kl}/{self.max_step}, close: {self.init_close}, cash: {self.cash}, shares: {self.shares}, asset: {self.total_asset}')

        if is_return:
            return self.get_state()

    def update_olhcv_features(self, df: pd.DataFrame):
        df = df.copy()
        df_cols = df.columns

        for col in self.olhcv_cols:
            # TODO: olhc_last should divide close? or self?
            if col == 'vol' or col == 'val':
                if col + '_max' in df_cols:
                    df[col + '_max'] = df[col] / (max(df[col]) + 1e-12)
                if col + '_last' in df_cols:
                    df[col + '_last'] = df[col] / (df[col].values[-1] + 1e-12)
            else:
                if col + '_max' in df_cols:
                    df[col + '_max'] = df[col] / (max(df['close']) + 1e-12)
                if col + '_last' in df_cols:
                    df[col + '_last'] = df[col] / (df['close'].values[-1] + 1e-12)

        return df[self.feature_columns]
    

    def get_state(self):
        # self.max_len = self.seq_len * self.fea_freqs
        x = self.data.iloc[self.cur_kl:self.cur_kl+self.max_len:self.fea_freqs][self.olhcv_cols + self.feature_columns]
        x = self.update_olhcv_features(x)
        state = x.values.astype(np.float32).flatten()
        # self.last_kl_offset = self.fea_freqs * (self.seq_len - 1)
        self.cur_close = self.data.iloc[self.cur_kl + self.last_kl_offset]['close']

        norm_usd_capital = self._capital_min_max_normalization(self.cash)
        norm_crypto_capital = self._crypto_min_max_normalization(self.shares)
        crypto_to_capital_ratio = self._map_crypto_price_to_capital(self.cur_close)
        # state = np.hstack((state, self.cash/self.total_asset, self.shares))
        state = np.hstack((state, norm_usd_capital, norm_crypto_capital, crypto_to_capital_ratio))
        return state
    

    def _capital_min_max_normalization(self, capital_in_usd: np.ndarray) -> np.ndarray:
        """min-max normalize the input vector"""
        min_value = np.array([self.initial_asset * self.capital_thresh], dtype=np.float32)
        max_value = np.array([self.initial_asset], dtype=np.float32)
        normalized_value = (capital_in_usd - min_value) / (max_value - min_value)
        return normalized_value
    
    def _crypto_min_max_normalization(self, capital_in_crypto: np.ndarray) -> np.ndarray:
        """min-max normalize the input vector w.r.t crypto start price"""
        min_value = np.array([0.0], dtype=np.float32)
        starting_purchase_capabilities = (
            self.initial_asset / self.init_close
        )
        max_value = np.array([starting_purchase_capabilities], dtype=np.float32)
        normalized_value = (capital_in_crypto - min_value) / (max_value - min_value)
        return normalized_value
    
    def _map_crypto_price_to_capital(self, crypto_price: np.ndarray) -> np.ndarray:
        """map min-max crypto value to capital and clip in range [ 0, 1 ]"""
        normalized_capital_map = self.initial_asset / (
            crypto_price + self.initial_asset
        )
        clipped_capital_map = np.clip(normalized_capital_map, 0, 1)
        return clipped_capital_map
    
    def calculate_commission(self, transaction_value):
        """calculate transaction loss based on a transaction value and fee"""
        transaction_loss = self.transaction_cost * transaction_value
        return transaction_loss
            
    def get_reward(self):
        self.reward_offset = 0   # 1e-6 # !!!
        reward = 0
        previous_close = self.cur_close
        self.cur_close = self.data.iloc[self.cur_kl]['close']

        previous_cash = self.cash
        previous_hold_volume = self.shares

        if self.cur_action > 0:
            # if we can not afford it
            open_cap = self.cur_action * self.max_open_capital

            buy_volume = min(previous_cash, open_cap) / previous_close / (1+self.transaction_cost)
            if buy_volume > 0 and open_cap > self.min_open_capital:
                self.episode_orders += 1
                self.cash = previous_cash - min(previous_cash, open_cap)
                self.shares = previous_hold_volume + buy_volume
                
        elif self.cur_action < 0:
            close_cap = -self.cur_action * self.max_close_capital
            sell_volume = min(previous_hold_volume, close_cap/previous_close)
            if sell_volume > 0 and close_cap > self.min_close_capital:
                self.episode_orders += 1
                self.cash = previous_cash + sell_volume * previous_close * (1 - self.transaction_cost)
                self.shares = previous_hold_volume - sell_volume

        self.next_total_asset = np.sum(self.cur_close * self.shares) + self.cash
        reward = self.next_total_asset - self.total_asset
        # if self.cur_action > 0:
        #     logger.info(f'{self.cur_action}, buy {buy_volume}, cash {self.cash}, shares {self.shares}')
        # else:  
        #     logger.info(f'{self.cur_action}, sell {sell_volume}, cash {self.cash}, shares {self.shares}')
        self.raw_reward_list.append(reward)
        self.reward_list.append(reward)

        return reward

    def step(self, action):
        self.cur_kl += 1
        self.cur_ts = self.data.iloc[self.cur_kl+self.last_kl_offset]['datatime']

        self.cur_action = action[0]
        reward = self.get_reward()

        # calculate the reward and next state
        state = self.get_state()
        self.cumulative_returns = self.next_total_asset / self.initial_asset # * 100

        self.total_asset = self.next_total_asset
        self.gamma_return = self.gamma_return * self.gamma + reward

        if reward < 0:
            reward *= self.neg_reward_scale

        self.prev_action = self.cur_action

        done = (self.cur_kl >= self.max_step) or (
            float(self.total_asset) <= self.initial_asset * self.capital_thresh)
        if done:
            reward = self.gamma_return
            self.gamma_return = 0.0
            logger.warning(f'{self.cur_kl}/{self.max_step}@{self.cur_ts}, {int(self.total_asset)}/{int(self.initial_asset)}, cash={int(self.cash)}, share={self.shares:.2f}, orders={self.episode_orders}, raw rewards={np.sum(self.raw_reward_list):.2f}, rewards={np.sum(self.reward_list):.2f}, gamma_return={reward:.2f}')

        return state, reward, done, None

