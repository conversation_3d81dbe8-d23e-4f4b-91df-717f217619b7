import numpy as np
import pandas as pd
import os
from loguru import logger
from functorch import vmap
from copy import deepcopy
from sklearn.preprocessing import StandardScaler
from elegantrl.utils import RewardScaling

# CTA V2: 0429
# 简化输入，仅输入模型分
score_thres_dict = {'DOGE#USDT:USDT': 0.47, 'XRP#USDT:USDT': 0.41, 'SOL#USDT:USDT': 0.47, 'ADA#USDT:USDT': 0.41, 'LINK#USDT:USDT': 0.47,
                    'LTC#USDT:USDT': 0.43, 'AVAX#USDT:USDT': 0.45, 'UNI#USDT:USDT': 0.50, 'DOT#USDT:USDT': 0.45, 'INJ#USDT:USDT': 0.46}

class CTAEnvV2:

    env_name = 'CTAEnv-V2'
    # reward_type = 'winrate'
    reward_type = 'profit'
    olhcv_cols = ['open', 'close', 'low', 'high', 'vol', 'val']
    cwd = f'data_snap/pred_model_5min_gain_v5_432882_online'
    ohlcv_dir = f'data_snap/portfolio_v1'
    feature_columns = [
        'score', 'pre_cr_1', 'pre_cr_3', 'pre_cr_5', 'pre_cr_15', 'pre_cr_30', 'pre_cr_60'
    ]
    score_thres = 0.4
    score_base = 0.1
    # filter_thres = 0.0
    # info = (norm_usd_capital, norm_crypto_capital, crypto_to_capital_ratio) 
    pos_frac = 5

    def __init__(self, **kwargs):
        symbols = kwargs.get('symbols', [])
        logger_path = kwargs.get('logger_path', '')
        if logger_path:
            logger.add(logger_path, level='INFO', enqueue=True)
        self.symbols = self._load_symbols(symbols)
        logger.info(f'train env: {self.env_name} on {self.symbols}')

        self.env_worker_id = kwargs.get('worker_id', -1)
        self.num_workers = kwargs.get('num_workers', 1)
        self.worker_slice = kwargs.get('worker_slice', True)
        self.transform_st = kwargs.get('transform_st', '2020.10.01')
        self.transform_et = kwargs.get('transform_et', '2022.10.01')
        self.feature_norm = kwargs.get('feature_norm', False)
        self.start_date = kwargs.get('start_date', '2020.10.01')
        self.end_date = kwargs.get('end_date', '2022.10.01')
        self.scaler = kwargs.get('scaler', {})
        self.initial_asset = kwargs.get('initial_asset', 1e6)
        self.initial_asset_base = self.initial_asset
        self.transaction_cost = kwargs.get('transaction_cost', 3e-4)
        self.forward_kl_nums = kwargs.get('forward_kl_nums', 5)
        self.max_open_capital = kwargs.get('max_open_capital', self.initial_asset_base/self.pos_frac)       # 最大open
        self.if_discrete = kwargs.get('if_discrete', False)
        self.action_dim = kwargs.get('action_dim', 1)
        self.min_action = kwargs.get('min_action', 0.1)
        self.open_thres = kwargs.get('open_thres', 0.0)
        self.max_holding_capital = kwargs.get('max_holding_capital', self.initial_asset_base)    # 最大hold
        self.capital_thresh = kwargs.get('capital_thresh', 0.3)     # 警戒线
        self.stop_loss = kwargs.get('stop_loss', 0.1)
        self.reward_base = kwargs.get('reward_base', '')
        self.seq_len = kwargs.get('seq_len', 1)
        self.fea_freqs = kwargs.get('fea_freqs', 1)
    
        self.gamma = kwargs.get('gamma', 0.999)
        self.random_reset = kwargs.get('random_reset', True)
        self.min_kl_nums = kwargs.get('min_kl_nums', 24*60*7)       # 最少KLine样本
        self.reward_scale = kwargs.get('reward_scale', 2**-8)
        self.neg_reward_scale = kwargs.get('neg_reward_scale', 1)
        self.target_return = kwargs.get('target_return', 200)
        self.env_max_step = kwargs.get('max_step', 24*60*365)  # 最大步数
        self.eval = kwargs.get('eval', False)
        logger.info(f'eval={self.eval}, transform data from {self.transform_st} to {self.transform_et}, feature_norm={self.feature_norm}, env data from {self.start_date} to {self.end_date}')
        # (state, pos, norm_usd_capital, norm_crypto_capital, crypto_to_capital_ratio)
        self.use_pos_info = kwargs.get('use_pos_info', True)
        if self.use_pos_info:
            self.info_dim = 2
        else:
            self.info_dim = 0

        self.fea_dim = len(self.feature_columns) * self.seq_len
        self.state_dim = self.fea_dim + self.info_dim
        self.last_kl_offset = self.fea_freqs * (self.seq_len - 1)
        self.max_len = self.seq_len * self.fea_freqs
        self.norm_nums = self.fea_dim # state中记录持有量

        self.dry_run = kwargs.get('dry_run', False)

        self.use_reward_scaling = kwargs.get('use_reward_scaling', False)
        if self.use_reward_scaling:
            logger.info(f'use reward scaling')
            self.reward_scaling = RewardScaling(shape=1, gamma=self.gamma)
            self.reward_scaling.reset()

        logger.info(f'fea_dim={self.fea_dim}, state_dim={self.state_dim}, last_kl_offset={self.last_kl_offset}, max_len={self.max_len}')

        self.reset(is_return=False)

    def _load_symbols(self, symbols:list[str]):
        if symbols == []:
            symbols = ['DOGE#USDT:USDT']
        filtered_symbols = []
        if os.path.exists(self.cwd):
            for sym in os.listdir(self.cwd):
                if sym in symbols:
                    filtered_symbols.append(sym)
            logger.debug(f'{filtered_symbols=}')
        else:

            logger.error('cwd not exist...')
            exit(-1)
        return filtered_symbols
    
    def _merge_olhcv(self, symbol, df):

        olhcv = pd.read_parquet(f'{self.ohlcv_dir}/{symbol}')
        olhcv = olhcv.drop_duplicates(subset=['datatime', 'symbol'])
        olhcv = olhcv.replace([np.inf, -np.inf], np.nan)
        olhcv.set_index('datatime', inplace=True)
        olhcv.sort_index(inplace=True)
        olhcv = olhcv.asfreq('60s', method="ffill")
        olhcv = olhcv.reset_index()
        for col in df.columns:
            if 'score' in col:
                df[col] = df[col].fillna(0)
        df = df.asfreq('60s', method="ffill")
        score = df.reset_index()
        return pd.merge(score, olhcv, how='inner', on=['symbol', 'datatime'])

    def _load_data(self, symbol):
        v = pd.read_parquet(f'{self.cwd}/{symbol}')
        v = v.drop_duplicates(subset=['datatime', 'symbol'])
        v = v.replace([np.inf, -np.inf], np.nan)
        v.set_index('datatime', inplace=True)
        v.sort_index(inplace=True)

        if self.feature_norm:
            transform_fit_data = v[self.transform_st:self.transform_et].copy()
            transform_df = self._merge_olhcv(symbol, transform_fit_data)
            if len(transform_df) == 0:
                logger.error(f'{symbol} has no data in {self.transform_st} to {self.transform_et}')
                return pd.DataFrame()
            transform_df = self._transform_features(transform_df)
            del transform_fit_data, transform_df

        v = v[self.start_date:self.end_date].copy()
        df = self._merge_olhcv(symbol, v)
        for col in self.feature_columns:
            if col in df.columns:
                df[col] = df[col].ffill()
            if 'pre_cr' in col:
                df[col] = np.log(df[col] + 1)
        if len(df) == 0:
            logger.error(f'empty data: {symbol}')
        if self.feature_norm:
            df = self._transform_features(df)
        return df.reset_index()

    def _transform_features(self, df):
        for fea in self.feature_columns:
            df['raw_' + fea] = df[fea]
            if fea not in self.scaler:
                scaler = StandardScaler()
                df[fea] = scaler.fit_transform(df[fea].values.reshape(-1, 1))
                self.scaler[fea] = scaler
                logger.info(f'{fea} scaler: {scaler.mean_}, {scaler.var_}, {scaler.scale_}')
            else:
                scaler = self.scaler[fea]
                df[fea] = scaler.transform(df[fea].values.reshape(-1, 1))
        return df

    def _gen_data_slice_idx(self):
        if not self.worker_slice:
            return None
        if self.env_worker_id < 0 or self.num_workers <= 1:
            return None
        data_len = len(self.data)
        if data_len <= self.num_workers:
            return None
        slice_len = data_len // self.num_workers
        start_idx = self.env_worker_id * slice_len
        end_idx = (self.env_worker_id + 1) * slice_len
        if self.env_worker_id == self.num_workers - 1:
            end_idx = data_len
        end_idx = data_len
        return start_idx, end_idx

    def reset(self, is_return=True):
        self.positon = []
        while True:
            self.cur_symbol = np.random.choice(self.symbols, 1)[0]
            self.data = self._load_data(self.cur_symbol)
            score_counts = []
            for col in self.data.columns:
                if 'score' in col:
                    score_counts.append(np.sum(self.data[col] > self.score_thres))
            score_count = np.mean(score_counts)
            if len(self.data) and score_count > 10:
                # logger.info(f'load {self.cur_symbol}, {len(self.data)} samples, score count: {score_count}')
                break
        self.score_thres = score_thres_dict.get(self.cur_symbol, 0.4)
        slice_idx = self._gen_data_slice_idx()

        if slice_idx:
            start_idx, end_idx = slice_idx
            self.data = self.data.iloc[start_idx:end_idx]
            logger.debug(f'id: {self.env_worker_id}, slice {self.cur_symbol} from {start_idx} to {end_idx}, len={len(self.data)}')
        else:
            logger.debug(f'id: {self.env_worker_id}, no slice for {self.cur_symbol}, len={len(self.data)}')
        self.start_kl = 0

        self.max_step = len(self.data) - self.forward_kl_nums - self.seq_len * self.fea_freqs
        self.cash = self.initial_asset_base
        self.shares = 0

        self.init_close = self.data.iloc[self.last_kl_offset]['close']
        
        if self.random_reset:
            if self.max_step - self.min_kl_nums > 0:
                self.start_kl = max(0, np.random.randint(0, self.max_step - self.min_kl_nums))
            self.data = self.data.iloc[self.start_kl:]
            self.init_close = self.data.iloc[self.last_kl_offset]['close']
            self.max_step = len(self.data) - self.forward_kl_nums - self.seq_len * self.fea_freqs
            self.initial_asset = self.initial_asset_base * np.random.uniform(0.5, 1.5)
            self.max_open_capital = self.initial_asset / self.pos_frac
            # self.shares = np.random.uniform(0, self.initial_asset/self.init_close/2)
            self.cash = self.initial_asset - self.shares * self.init_close
            self.positon.append((self.data.iloc[self.last_kl_offset]['datatime'], self.shares))
            # self.initial_asset = np.sum(self.init_close * self.shares) + self.cash
        # logger.info(f'reset score thres={self.score_thres}')
        self.max_step = min(self.max_step, self.env_max_step)
        self.cur_kl = 0
        self.cur_ts = self.data.iloc[self.cur_kl+self.last_kl_offset]['datatime']
        self.cur_action = 0
        self.cur_state = None
        self.prev_action = 0
        self.gamma_return = 0.0
        self.cumulative_returns = 0.0
        self.episode_orders = 0
        self.prev_episode_orders = 0
        self.reward_list = []
        self.open_reward = []
        self.hold_reward = []
        self.open_action = []
        self.hold_action = []
        self.raw_reward_list = []
        self.cur_action_list = []
        self.base_return_list = []
        self.oracle_return_list = []
        self.score_list = []

        self.stat_info = {'skip': 0, 'open': 0, 'hold': 0}
        self.cur_close = self.init_close
        self.next_close = self.init_close
        self.sell_close = self.init_close
        self.total_asset = self.initial_asset
        self.next_total_asset = 0
        logger.debug(f'random reset {self.random_reset}, start: {self.start_kl}/{self.max_step}@{self.cur_symbol}, close: {self.init_close}, cash: {self.cash}, shares: {self.shares}, asset: {self.total_asset}')
        if self.use_reward_scaling:
            self.reward_scaling.reset()
        if is_return:
            return self.get_state(), {}

    def get_state(self):
        # self.max_len = self.seq_len * self.fea_freqs
        # raw_fea_cols = ['raw_' + fea for fea in self.feature_columns]
        # if not self.eval and self.filter_thres:
        #     while True:
        #         raw_x = self.data.iloc[self.cur_kl:self.cur_kl+self.max_len:self.fea_freqs][raw_fea_cols]
        #         raw_x = raw_x.values.astype(np.float32).flatten()
        #         if self.cur_kl>=self.max_step or raw_x[0] >= self.filter_thres:
        #             break
        #         self.cur_kl += 1
        #         cur_kl = self.cur_kl+self.last_kl_offset
        #         self.cur_ts = self.data.iloc[cur_kl]['datatime']
        #         self.clean_position()
        # else:
        # raw_x = self.data.iloc[self.cur_kl:self.cur_kl+self.max_len:self.fea_freqs][raw_fea_cols]
        # raw_x = raw_x.values.astype(np.float32).flatten()
        feature_columns = []
        for fea in self.feature_columns:
            if fea in self.data.columns:
                feature_columns.append(fea)
        x = self.data.iloc[self.cur_kl:self.cur_kl+self.max_len:self.fea_freqs][feature_columns]
        state = x.values.astype(np.float32).flatten()

        # self.cur_close = self.data.iloc[self.cur_kl + self.last_kl_offset]['close']
        if self.use_pos_info:
            norm_usd_capital = self._capital_min_max_normalization(self.cash)
            norm_crypto_capital = self._crypto_min_max_normalization(self.shares*self.cur_close)
            # crypto_to_capital_ratio = self._map_crypto_price_to_capital(self.cur_close)
            state = np.hstack((state, norm_usd_capital, norm_crypto_capital))
        self.raw_state = state
        self.cur_state = state
        return state
    

    def _capital_min_max_normalization(self, capital_in_usd: np.ndarray) -> np.ndarray:
        """min-max normalize the input vector"""
        # min_value = np.array([self.initial_asset * self.capital_thresh], dtype=np.float32)
        # max_value = np.array([self.initial_asset], dtype=np.float32)
        normalized_value = (capital_in_usd - self.initial_asset * self.capital_thresh) / (self.initial_asset - self.initial_asset * self.capital_thresh)
        # normalized_value = capital_in_usd / self.initial_asset
        return normalized_value


    def _crypto_min_max_normalization(self, capital_in_crypto: np.ndarray) -> np.ndarray:
        """min-max normalize the input vector w.r.t crypto start price"""
        # min_value = np.array([0.0], dtype=np.float32)
        # starting_purchase_capabilities = (
        #     self.initial_asset / self.init_close
        # )
        # max_value = np.array([starting_purchase_capabilities], dtype=np.float32)
        # max_purchase_capabilities = (
        #     self.max_open_capital / self.cur_close
        # )
        # max_value = np.array([max_purchase_capabilities], dtype=np.float32)
        # normalized_value = (capital_in_crypto - min_value) / (max_value - min_value)

        normalized_value = capital_in_crypto / self.total_asset
        return normalized_value
    
    def _map_crypto_price_to_capital(self, crypto_price: np.ndarray) -> np.ndarray:
        """map min-max crypto value to capital and clip in range [ 0, 1 ]"""
        # normalized_capital_map = self.initial_asset / (
        #     crypto_price + self.initial_asset
        # )
        normalized_capital_map = self.max_open_capital / (
            crypto_price + self.max_open_capital
        )
        # normalized_capital_map = crypto_price / self.max_open_capital
        clipped_capital_map = np.clip(normalized_capital_map, 0, 1)
        return clipped_capital_map
    
    def calculate_commission(self, transaction_value):
        """calculate transaction loss based on a transaction value and fee"""
        transaction_loss = self.transaction_cost * transaction_value
        return transaction_loss

    def clean_position(self, buy_volume=0, dry_run=False):
        """clean the position if the time is over 5 minutes"""
        cur_position = deepcopy(self.positon)
        for pos in cur_position:
            if self.cur_ts - pos[0] >= pd.Timedelta(minutes=self.forward_kl_nums):
                wait_to_clean_pos = pos[1]
                sell_volume = max(0, wait_to_clean_pos - buy_volume)

                buy_volume = max(0, buy_volume-wait_to_clean_pos)

                if not dry_run:
                    # sell
                    if sell_volume > 0:
                        self.episode_orders += 1
                    self.cash += sell_volume * self.cur_close * (1-self.transaction_cost)
                    self.shares -= sell_volume
                    logger.debug(f'clean {pos}, sell {sell_volume}, wait to buy {buy_volume}, cash={self.cash}, share={self.shares}')
                    self.positon.remove(pos)
        return buy_volume

    def get_reward(self):
        base_return = 0
        oracle_return = 0
        score = self.cur_state[0]
        self.score_list.append(score)
        sell_close = self.sell_close

        # base by score
        if score > self.score_thres:
            # raw_open_cap = max_open_capital * max(self.min_action, (score - self.score_base))
            raw_open_cap = self.max_open_capital * (score - self.score_base)
            # raw_open_cap = min(self.cash, self.max_open_capital * (score - self.score_base))
            buy_volume = raw_open_cap / self.cur_close
            # wait_to_buy_volume = self.clean_position(buy_volume, dry_run=True)
            wait_to_buy_volume = buy_volume
            possible_profit_with_cost = sell_close * (1-self.transaction_cost)**2 - self.cur_close
            possible_profit_without_cost = sell_close * (1-self.transaction_cost) - self.cur_close
            base_return = possible_profit_with_cost * wait_to_buy_volume + \
                    possible_profit_without_cost * (buy_volume - wait_to_buy_volume)
        
        # base by oracle
        # buy_volume = min(self.cash, self.max_open_capital) / self.cur_close
        buy_volume = self.max_open_capital / self.cur_close
        # wait_to_buy_volume = self.clean_position(buy_volume, dry_run=True)
        wait_to_buy_volume = buy_volume
        possible_profit_with_cost = sell_close * (1-self.transaction_cost)**2 - self.cur_close
        possible_profit_without_cost = sell_close * (1-self.transaction_cost) - self.cur_close
        oracle_return = possible_profit_with_cost * wait_to_buy_volume + \
                possible_profit_without_cost * (buy_volume - wait_to_buy_volume)
        if oracle_return < 0: oracle_return = 0
        
        self.base_return_list.append(base_return)
        self.oracle_return_list.append(max(0, oracle_return))
        encourage_penalty = 1e-5 * self.total_asset

        # if score < self.score_thres - 0.1:
        #     self.stat_info['skip'] += 1
        #     reward = 0
        #     _ = self.clean_position()
        # elif self.cur_action > self.min_action:

        if self.cur_action > self.min_action and score >= self.open_thres:
            reward = 0
            self.stat_info['open'] += 1
            open_cap = self.max_open_capital * self.cur_action
            # open_cap = min(self.cash, self.max_open_capital * self.cur_action)
            # buy_volume = open_cap * (1 - self.transaction_cost) / self.cur_close
            buy_volume = open_cap / self.cur_close
            wait_to_buy_volume = self.clean_position(buy_volume)
            if wait_to_buy_volume > 0:
                if wait_to_buy_volume * self.cur_close > self.cash:
                    buy_volume -= wait_to_buy_volume
                    wait_to_buy_volume = self.cash / self.cur_close
                    buy_volume += wait_to_buy_volume
                    # reward -= encourage_penalty
                self.episode_orders += 1
                self.cash -= wait_to_buy_volume * self.cur_close
                self.shares += wait_to_buy_volume * (1 - self.transaction_cost)
            if buy_volume > 0:
                real_buy_volume = buy_volume - wait_to_buy_volume * self.transaction_cost
                self.positon.append((self.cur_ts, real_buy_volume))
            possible_profit_with_cost = sell_close * (1-self.transaction_cost)**2 - self.cur_close
            possible_profit_without_cost = sell_close * (1-self.transaction_cost) - self.cur_close

            reward += possible_profit_with_cost * wait_to_buy_volume + (buy_volume - wait_to_buy_volume) * possible_profit_without_cost
            # reward -= base_return

            # if score > self.score_thres:
            #     reward = float(self.cur_action * 10)
            # else:
            #     reward = -100
            # if oracle_return > 0:
            #     reward -= oracle_return
            # logger.debug(f'{self.cur_ts}, action={self.cur_action}, {buy_volume=}, price={self.cur_close}, {open_cap=}, cash={self.cash}, share={self.shares}, reward={reward}')
            # if reward > 0:
            #     reward *= 10
        # elif self.cur_action <= -self.min_action:
            # possible_profit = self.data.iloc[self.cur_kl+self.forward_kl_nums-1]['close'] * (1-self.transaction_cost)**2 /self.cur_close-1
            # if possible_profit > 0:
            #     if self.cur_action_list:
            #         reward = -possible_profit * min(self.cash, self.max_open_capital) * np.mean(self.cur_action_list)
            #     else:
            #         reward = -possible_profit * min(self.cash, self.max_open_capital) * 0.5
            # else:
            #     reward = 0
            # if possible_profit > 0:
            #     reward = -5
            # else:
            #     reward = 5
            # _ = self.clean_position()
        else:
            self.stat_info['hold'] += 1
            # reward = -oracle_return
            reward = 0
            # if oracle_return > 0:
            #     reward = -oracle_return
            # reward = min(0, -base_return)
            # reward = -base_return - encourage_penalty
            # if score < self.score_thres:
            #     reward = float(-self.cur_action * 10)
            # else:
            #     reward = -100

            # reward = -encourage_penalty
            _ = self.clean_position()

            # if score > self.score_thres and reward < 0:
            #     reward *= 10
        self.next_total_asset = float(np.sum(self.next_close * self.shares) + self.cash)
        reward = float(self.next_total_asset - self.total_asset)
        if self.eval:
            if np.random.rand() < 0.001:
                logger.info(f'{self.cur_ts}, state={self.cur_state}, action={self.cur_action}, cash={self.cash}, share={self.shares}, reward={reward}, total_asset={self.total_asset}, episode_orders={self.episode_orders}')


        if self.reward_type == 'winrate':
            if reward < 0:
                # 有open情况下，没有超过阈值收益，惩罚-1
                if self.cur_action > self.min_action:
                    reward = -1
                # hold情况下，超过阈值但没有躲过亏损，惩罚-100
                elif score > self.score_thres:
                    reward = -100
                # hold，没有超过阈值，惩罚-1
                else:
                    reward = -10
            # 有超额收益，奖励+1
            elif reward > 0:
                reward = 1

        # self.raw_reward_list.append(reward)
        self.reward_list.append(reward)
        if self.cur_action > self.min_action:
            self.open_reward.append(reward)
            self.open_action.append(self.cur_action)
        else:
            self.hold_reward.append(reward)
            self.hold_action.append(self.cur_action)
        return reward
    
    def _norm_action(self, action):
        # action = np.clip(action, 0, 1)
        if self.action_dim > 1:
            action = action / self.action_dim
        else:
            action = action[0]
            # action = (action + 1) / 2  # scale to [0, 1]
        return action
    
    def step(self, action):
        self.cur_kl += 1
        cur_kl = self.cur_kl+self.last_kl_offset
        self.cur_ts = self.data.iloc[cur_kl]['datatime']
        self.cur_close = self.data.iloc[cur_kl-1]['close']
        self.next_close = self.data.iloc[cur_kl]['close']
        self.sell_close = self.data.iloc[cur_kl+self.forward_kl_nums-1]['close']

        self.cur_action = self._norm_action(action)
        self.cur_action_list.append(self.cur_action)

        reward = self.get_reward()
        state = self.get_state()
        self.total_asset = self.next_total_asset
        self.cumulative_returns = self.total_asset / self.initial_asset * 100
        self.gamma_return = float(self.gamma_return * self.gamma + reward)
        self.prev_action = self.cur_action
        done = (self.cur_kl >= self.max_step) or (
            float(self.total_asset) <= float(self.initial_asset * self.capital_thresh))
        if done:

            self.next_total_asset = float(np.sum(self.sell_close * self.shares) + self.cash)
            self.cumulative_returns = self.next_total_asset / self.initial_asset * 100
            hold_ret = float(self.next_close / self.init_close)
            # reward = self.gamma_return
            # reward += (self.cumulative_returns - 100) * 100
            if self.base_return_list:
                base_ret = 100 * (np.sum(self.base_return_list) / self.initial_asset + 1)
            else:
                base_ret = 100
            if self.oracle_return_list:
                oracle_ret = 100 * (np.sum(self.oracle_return_list) / self.initial_asset + 1)
            else:
                oracle_ret = 100

            # reward += 1 / (1 - self.gamma) * np.mean(self.reward_list)
            reward = (self.next_total_asset - self.total_asset) * self.reward_scale
            self.total_asset = self.next_total_asset
            other_logging_str = ''
            if self.base_return_list:
                other_logging_str += f', base_ret={base_ret:.2f}'
            if self.oracle_return_list:
                other_logging_str += f', oracle_ret={oracle_ret:.2f}'
            if self.cur_action_list:
                other_logging_str += f', action_avg={np.mean(self.cur_action_list):.2f}, action_std={np.std(self.cur_action_list):.2}'
            if self.score_list:
                other_logging_str += f', score>{self.score_thres:.2f}: {np.sum(np.array(self.score_list)>self.score_thres)}'
            # other_logging_str += f', reward stat: min={np.min(self.reward_list):.2f}, max={np.max(self.reward_list):.2f}, avg={np.mean(self.reward_list):.2f}, std={np.std(self.reward_list):.2f}, {self.stat_info}'
            other_logging_str += f', reward stat: >0= {np.sum(np.array(self.reward_list)>0):.2f}, 0={np.sum(np.array(self.reward_list)==0):.2f}, <0={np.sum(np.array(self.reward_list)<0):.2f}, avg={np.mean(self.reward_list):.2f}, std={np.std(self.reward_list):.2f}'
            other_logging_str += f', hold_reward={np.mean(self.hold_reward):.2f}, open_reward={np.mean(self.open_reward):.2f}'
            other_logging_str += f', hold_action={np.mean(self.hold_action):.2f}, open_action={np.mean(self.open_action):.2f}'
            other_logging_str += f', {self.stat_info}'

            if self.eval:
                logger.warning(f'{self.cur_kl}/{self.max_step}@{self.cur_ts}, {self.cur_symbol}, {int(self.total_asset)}/{int(self.initial_asset)}, cash={int(self.cash)}, share={self.shares:.2f}, orders={self.episode_orders}, cum_ret={self.cumulative_returns:.2f}, {hold_ret=:.2f}, raw rewards={np.sum(self.reward_list):.2f}, final reward={reward:.2f}{other_logging_str}, {self.gamma_return=}')
            else:
                logger.info(f'{self.cur_kl}/{self.max_step}@{self.cur_ts}, {self.cur_symbol}, {int(self.total_asset)}/{int(self.initial_asset)}, cash={int(self.cash)}, share={self.shares:.2f}, orders={self.episode_orders}, cum_ret={self.cumulative_returns:.2f}, {hold_ret=:.2f}, raw rewards={np.sum(self.reward_list):.2f}, final reward={reward:.2f}{other_logging_str}, {self.gamma_return=}')
            self.gamma_return = 0.0

        truncated = False
        return state, reward, done, truncated, {}

