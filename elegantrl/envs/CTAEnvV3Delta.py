import numpy as np
from loguru import logger
from elegantrl.envs.CTAEnvV2 import CTAEnvV2

# CTA V3: 优化版本
# 修复了手续费计算、时间对齐和奖励函数的问题
class CTAEnvV3Delta(CTAEnvV2):
    """
    CTAEnvV3: 优化的数字货币交易强化学习环境

    主要改进：
    1. 修复手续费计算逻辑，确保在不同交易场景下的准确性
    2. 修复时间对齐问题，确保价格和时间戳的一致性
    3. 优化奖励函数设计，提供更合理的激励机制
    4. 增强错误处理和数值稳定性
    5. 改进日志记录和调试信息

    特性：
    - 支持做多做空交易
    - 基于模型评分的特征输入
    - 可配置的奖励类型（绝对/相对）
    - 完善的交易成本计算
    """

    env_name = 'CTAEnv-V3-Delta'
    olhcv_cols = ['open', 'close', 'low', 'high', 'vol', 'val']
    cwd = f'data_snap/pred_model_5min_gain_v5_432882_online'
    ohlcv_dir = f'data_snap/portfolio_v1'
    feature_columns = [
        'score', 'pre_cr_1', 'pre_cr_3', 'pre_cr_5', 'pre_cr_15', 'pre_cr_30', 'pre_cr_60',
    ]
    score_base = 0.1
    eval_print_ratio = 0.00001
    norm_nums = len(feature_columns) # state中记录持有量
    # info = (norm_usd_capital, norm_crypto_capital, crypto_to_capital_ratio) 
    info_dim = 2

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.returns_history = []
        self.risk_free_rate = kwargs.get('risk_free_rate', 0.05)  #
        self.sharpe_windows = kwargs.get('sharpe_windows', 60)
        self.enable_short = kwargs.get('enable_short', False)
        self.reward_type = kwargs.get('reward_type', '')    # 'relative'
        if self.reward_type == '':
            self.reward_type = 'absolute'
        logger.info(f'set {self.reward_type=}, {len(self.feature_columns)=}, {self.norm_nums=}, {self.info_dim=}')
        self.min_action = kwargs.get('min_action', 0.05)   # 最小动作
        self.encourage_penalty = kwargs.get('encourage_penalty', 0)

    def reset(self, is_return=True):
        """
        重置环境状态，初始化所有必要的变量
        """
        # 重置V3特有的状态变量
        self.prev_pos = 0
        self.cur_state = None
        self.pos_list = []
        self.turnover_list = []
        self.close_reward = []
        self.returns_history = []
        # 调用父类的reset方法
        result = CTAEnvV2.reset(self, is_return)

        # 验证初始化状态的有效性
        if hasattr(self, 'total_asset') and self.total_asset <= 0:
            logger.warning(f"Invalid initial total_asset: {self.total_asset}, resetting to initial_asset")
            self.total_asset = self.initial_asset
            self.cash = self.initial_asset
            self.shares = 0

        # 确保价格数据有效
        if hasattr(self, 'cur_close') and (not np.isfinite(self.cur_close) or self.cur_close <= 0):
            logger.error(f"Invalid current close price: {self.cur_close}")
            raise ValueError("Invalid price data detected during reset")

        return result
    

    def get_reward(self):
        """
        计算奖励函数，修复手续费计算和时间对齐问题
        """
        diff_pos = self.cur_action
        prev_pos = self.shares * self.cur_close / self.total_asset if self.total_asset > 0 else 0
        prev_shares = self.shares
        diff_pos = np.clip(diff_pos, -prev_pos, 1-prev_pos)
        cur_pos = prev_pos + diff_pos

        score = self.raw_state[0]
        reward = 0
        transaction_cost = 0  # 记录本次交易成本

        if abs(diff_pos) > self.min_action:  # 有实际交易
            # 计算目标仓位（股票数量）
            target_shares = cur_pos * self.total_asset / self.cur_close if self.cur_close > 0 else 0

            # 计算需要交易的股票数量
            trade_shares = target_shares - prev_shares

            # 验证交易可行性并获取调整后的交易数量
            is_valid, adjusted_trade_shares = self._validate_transaction(trade_shares, self.cur_close)

            if is_valid and abs(adjusted_trade_shares) > 1e-8:  # 有效交易且数量不为零
                # 计算交易成本
                transaction_cost = self._calculate_transaction_cost(adjusted_trade_shares, self.cur_close)

                # 执行交易
                if adjusted_trade_shares > 0:  # 买入
                    cost = adjusted_trade_shares * self.cur_close + transaction_cost
                    self.cash -= cost
                    self.shares += adjusted_trade_shares

                else:  # 卖出
                    sell_shares = abs(adjusted_trade_shares)
                    proceeds = sell_shares * self.cur_close - transaction_cost
                    self.cash += proceeds
                    self.shares -= sell_shares

                # 检查资产平衡
                # self._check_asset_balance()

                # 记录交易信息
                actual_pos = self.shares * self.cur_close / self.total_asset if self.total_asset > 0 else 0
                self.pos_list.append(actual_pos)
                self.turnover_list.append(abs(adjusted_trade_shares * self.cur_close / self.total_asset))
                self.episode_orders += 1
                self.prev_pos = actual_pos
                self.prev_state = self.cur_state
            else:
                # 无法执行交易，保持原有仓位
                self.pos_list.append(prev_pos)
                self.turnover_list.append(0)
                transaction_cost = 0
        else:
            # 无交易，保持原有仓位
            self.pos_list.append(prev_pos)
            self.turnover_list.append(0)
            # 对无交易行为给予小幅惩罚，鼓励积极交易
            # reward -= self.encourage_penalty * self.total_asset

        # 计算下一期总资产（考虑价格变化）
        self.next_total_asset = float(self.shares * self.next_close + self.cash)

        # 基础奖励：资产变化
        asset_change = self.next_total_asset - self.total_asset
        

        # 相对奖励：与基准策略比较
        if self.reward_type == 'asset':
            reward = asset_change
            # # 计算持有策略的收益作为基准
            # if self.total_asset > 0:
            #     hold_return = prev_shares * (self.next_close - self.cur_close)
            #     reward -= hold_return
        elif self.reward_type == 'sharpe':
            # Track returns for risk metrics
            period_return = asset_change / self.total_asset
            self.returns_history.append(period_return)
            if len(self.returns_history) >= self.sharpe_windows:
                returns_array = np.array(self.returns_history[-self.sharpe_windows:])
                if np.std(returns_array) > 0:
                    sharpe_ratio = (np.mean(returns_array) - self.risk_free_rate / 525600) / np.std(returns_array)
                    reward = sharpe_ratio

        # 风险调整：对过度交易进行惩罚
        # if transaction_cost > 0:
        #     reward -= transaction_cost  # 扣除交易成本

        # 记录不同类型的奖励
        if cur_pos > 0:
            self.open_reward.append(reward)
        elif cur_pos < 0:
            self.close_reward.append(reward)
        else:
            self.hold_reward.append(reward)

        # 调试信息
        if self.eval and np.random.rand() < self.eval_print_ratio:
            logger.info(f'{self.cur_ts}, state={self.raw_state[:3]}, pos={diff_pos:.3f}, '
                       f'cash={self.cash:.2f}, shares={self.shares:.4f}, '
                       f'reward={reward:.4f}, total_asset={self.total_asset:.2f}, '
                       f'transaction_cost={transaction_cost:.4f}')

        self.reward_list.append(reward)
        return reward


    def _norm_action(self, action):
        """
        标准化动作，确保动作在合理范围内
        """
        if self.action_dim > 1:
            action = action / self.action_dim
        else:
            action = np.clip(action[0], -1, 1)

        # 确保动作是有限数值
        if not np.isfinite(action):
            logger.warning(f"Invalid action detected: {action}, setting to 0")
            action = 0.0

        return float(action)
    

    def step(self, action):
        """
        执行一步交易，修复时间对齐问题
        """
        self.cur_kl += 1
        cur_kl = self.cur_kl + self.last_kl_offset

        # 修复时间对齐：确保时间戳和价格的一致性
        # 当前时间戳对应当前价格，下一期时间戳对应下一期价格
        self.cur_ts = self.data.iloc[cur_kl-1]['datatime']  # 当前决策时间点
        self.cur_close = self.data.iloc[cur_kl-1]['close']   # 当前价格（决策时已知）
        self.next_close = self.data.iloc[cur_kl]['close']    # 下一期价格（奖励计算用）

        # 标准化动作
        self.cur_action = self._norm_action(action)
        self.cur_action_list.append(self.cur_action)

        # 计算奖励
        reward = self.get_reward()

        # 获取下一个状态
        state = self.get_state()

        # 更新资产和统计信息
        self.cumulative_returns = self.next_total_asset / self.initial_asset * 100
        self.total_asset = self.next_total_asset
        self.gamma_return = float(self.gamma_return * self.gamma + reward)
        self.prev_action = self.cur_action

        # 检查是否结束
        done = (self.cur_kl >= self.max_step) or (
            float(self.total_asset) <= float(self.initial_asset * self.capital_thresh))

        if done:
            # 计算最终统计信息
            hold_ret = float(self.next_close / self.init_close)

            # 计算基准收益
            if self.base_return_list:
                base_ret = 100 * (np.sum(self.base_return_list) / self.initial_asset + 1)
            else:
                base_ret = 100

            # 最终奖励调整
            final_reward_adjustment = 1 / (1 - self.gamma) * np.mean(self.reward_list) if self.reward_list else 0
            reward += final_reward_adjustment

            # 构建日志信息
            other_logging_str = self._build_logging_string(base_ret)

            # 记录最终结果
            log_msg = (f'{self.cur_kl}/{self.max_step}@{self.cur_ts}, {self.cur_symbol}, '
                      f'{int(self.total_asset)}/{int(self.initial_asset)}, '
                      f'cash={int(self.cash)}, share_value={self.shares*self.cur_close:.2f}, '
                      f'orders={self.episode_orders}, cum_ret={self.cumulative_returns:.2f}, '
                      f'hold_ret={hold_ret:.2f}, raw_rewards={np.sum(self.reward_list):.2f}, '
                      f'final_reward={reward:.2f}{other_logging_str}')

            if self.eval:
                logger.warning(log_msg)
            else:
                logger.info(log_msg)

            self.gamma_return = 0.0

        truncated = False
        return state, reward, done, truncated, {}

    def _build_logging_string(self, base_ret):
        """构建日志字符串"""
        other_logging_str = f', base_ret={base_ret:.2f}'

        if self.cur_action_list:
            other_logging_str += f', action_avg={np.mean(self.cur_action_list):.2f}, action_std={np.std(self.cur_action_list):.2f}'
        if self.pos_list:
            other_logging_str += f', pos_avg={np.mean(self.pos_list):.2f}'
        if self.turnover_list:
            other_logging_str += f', turnover_avg={np.mean(self.turnover_list):.2f}'

        if self.reward_list:
            other_logging_str += (f', reward_stat: min={np.min(self.reward_list):.2f}, '
                                f'max={np.max(self.reward_list):.2f}, '
                                f'avg={np.mean(self.reward_list):.2f}, '
                                f'std={np.std(self.reward_list):.2f}')

        # 分类奖励统计
        if self.open_reward:
            other_logging_str += f', open_reward={np.sum(self.open_reward):.2f}/{len(self.open_reward)}'
        if self.close_reward:
            other_logging_str += f', close_reward={np.sum(self.close_reward):.2f}/{len(self.close_reward)}'
        if self.hold_reward:
            other_logging_str += f', hold_reward={np.sum(self.hold_reward):.2f}/{len(self.hold_reward)}'
        if self.returns_history:
            returns_array = np.array(self.returns_history)
            sharpe_ratio = float((np.mean(returns_array) - self.risk_free_rate / 525600) / (np.std(returns_array) + 1e-8))
            other_logging_str += f', sharpe_ratio={sharpe_ratio:.4f}'

        return other_logging_str

    def _calculate_transaction_cost(self, trade_shares, price):
        """
        计算交易成本

        Args:
            trade_shares: 交易股票数量（正数为买入，负数为卖出）
            price: 交易价格

        Returns:
            transaction_cost: 交易成本
        """
        trade_value = abs(trade_shares) * price
        return trade_value * self.transaction_cost

    def _validate_transaction(self, trade_shares, price):
        """
        验证交易是否可行

        Args:
            trade_shares: 交易股票数量
            price: 交易价格

        Returns:
            (is_valid, adjusted_shares): 是否有效，调整后的交易数量
        """
        if trade_shares > 0:  # 买入
            cost = trade_shares * price
            transaction_cost = self._calculate_transaction_cost(trade_shares, price)
            total_cost = cost + transaction_cost

            if self.cash >= total_cost:
                return True, trade_shares
            else:
                # 资金不足，计算最大可买入数量
                max_shares = (self.cash - transaction_cost) / price
                logger.debug(f"资金不足，无法买入 {trade_shares:.4f}, 买入 {max_shares:.4f}")
                if max_shares > 0:
                    return True, max_shares
                else:
                    return False, 0
        else:  # 卖出
            sell_shares = abs(trade_shares)
            if self.shares >= sell_shares or self.enable_short:
                return True, trade_shares
            else:
                # 持股不足，最多卖出全部持股
                logger.debug(f"持股不足，无法卖出 {sell_shares:.4f}, 卖出 {self.shares:.4f}")
                return True, -self.shares if self.shares > 0 else 0

    def _check_asset_balance(self):
        """
        检查资产平衡，确保现金+股票价值=总资产
        """
        calculated_total = self.cash + self.shares * self.cur_close
        tolerance = 1e-6  # 允许的误差范围

        if abs(calculated_total - self.total_asset) > tolerance:
            logger.warning(f"资产不平衡: 计算总资产={calculated_total:.6f}, "
                          f"记录总资产={self.total_asset:.6f}, "
                          f"差异={abs(calculated_total - self.total_asset):.6f}")
            # 以计算值为准，修正总资产
            self.total_asset = calculated_total

    def get_portfolio_stats(self):
        """
        获取投资组合统计信息
        """
        if not self.reward_list:
            return {}

        rewards = np.array(self.reward_list)
        returns = rewards / self.initial_asset if self.initial_asset > 0 else rewards

        stats = {
            'total_return': self.cumulative_returns - 100,
            'total_trades': self.episode_orders,
            'avg_reward': np.mean(rewards),
            'reward_std': np.std(rewards),
            'sharpe_ratio': np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252 * 24 * 60),  # 年化夏普比率
            'max_drawdown': self._calculate_max_drawdown(),
            'win_rate': np.sum(rewards > 0) / len(rewards) if len(rewards) > 0 else 0,
            'avg_position': np.mean(self.pos_list) if self.pos_list else 0,
            'avg_turnover': np.mean(self.turnover_list) if self.turnover_list else 0,
        }

        return stats

    def _calculate_max_drawdown(self):
        """
        计算最大回撤
        """
        if not self.reward_list:
            return 0

        cumulative_returns = np.cumsum(self.reward_list)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / (running_max + self.initial_asset)
        return np.min(drawdown) if len(drawdown) > 0 else 0

    def validate_state(self):
        """
        验证当前状态的有效性
        """
        issues = []

        # 检查资产
        if not np.isfinite(self.total_asset) or self.total_asset <= 0:
            issues.append(f"Invalid total_asset: {self.total_asset}")

        # 检查现金和股票
        if not np.isfinite(self.cash):
            issues.append(f"Invalid cash: {self.cash}")
        if not np.isfinite(self.shares):
            issues.append(f"Invalid shares: {self.shares}")

        # 检查价格
        if not np.isfinite(self.cur_close) or self.cur_close <= 0:
            issues.append(f"Invalid cur_close: {self.cur_close}")

        # 检查动作
        if hasattr(self, 'cur_action') and not np.isfinite(self.cur_action):
            issues.append(f"Invalid cur_action: {self.cur_action}")

        if issues:
            logger.error(f"State validation failed: {issues}")
            return False

        return True


