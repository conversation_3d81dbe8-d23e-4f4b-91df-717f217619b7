import numpy as np
from loguru import logger
from elegantrl.envs.CTAEnvV2 import CTAEnvV2
from typing import Dict, List, Tuple
# CTA V3: 04206129
# 简化输入，仅输入模型分
class CTAEnvV3(CTAEnvV2):

    env_name = 'CTAEnv-V3'
    olhcv_cols = ['open', 'close', 'low', 'high', 'vol', 'val']
    cwd = f'data_snap/pred_model_5min_gain_v5_432882_online'
    ohlcv_dir = f'data_snap/portfolio_v1'
    feature_columns = [
        'score', 'pre_cr_1', 'pre_cr_3', 'pre_cr_5', 'pre_cr_15', 'pre_cr_30', 'pre_cr_60',
        
        # Volatility features
        'volatility_5m', 'volatility_15m', 'volatility_60m', 'volatility_240m', 'realized_vol', 'vol_ratio',

        # Technical indicators
        'rsi_14', 'rsi_30', 'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'bb_width',
        'ema_12', 'ema_26', 'sma_50', 'momentum_10',

        # Volume features
        'volume_ratio', 'volume_sma_ratio', 'vwap_ratio', 'volume_volatility',

        # Market microstructure (if available)
        'trade_intensity',

        # Cross-timeframe features
        'trend_alignment', 'support_resistance', 'breakout_signal'
    ]

    ta_params = {
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'bb_period': 20,
            'bb_std': 2.0,
            'ema_short': 12,
            'ema_long': 26,
            'sma_period': 50,
            'momentum_period': 10
    }

    score_thres = 0.43
    score_base = 0.1
    eval_print_ratio = 0.0001
    norm_nums = len(feature_columns) # state中记录持有量
    info_dim = 8

    def __init__(self, **kwargs):
        self.volatility_lookback = kwargs.get('volatility_lookback', 20)
        self.volatility_target = kwargs.get('volatility_target', 0.40)  # 20% annual
        self.min_holding_periods = kwargs.get('min_holding_periods', 5)  # Minimum periods to hold
        self.peak_value = kwargs.get('initial_asset', 100000)
        self.returns_history = []
        self.current_drawdown = 0.0
        # NEW: HOLDING TIME TRACKING VARIABLES
        self.position_holding_time = 0  # Current position holding time
        self.trading_frequency_history = []  # History of trading frequency

        super().__init__(**kwargs)
        self.enable_short = kwargs.get('enable_short', False)
        self.reward_type = kwargs.get('reward_type', '')    # 'relative'
        if self.reward_type == '':
            self.reward_type = 'absolute'
        logger.info(f'set {self.reward_type=}, {len(self.feature_columns)=}, {self.norm_nums=}, {self.info_dim=}')
        self.min_action = kwargs.get('min_action', 0.05)   # 最小动作
        self.encourage_penalty = kwargs.get('encourage_penalty', 0)

    def reset(self, is_return=True):
        
        self.prev_pos = 0
        self.cur_state = None
        self.pos_list = []
        self.turnover_list = []
        self.close_reward = []
        self.returns_history = []
        self.current_drawdown = 0.0
        # NEW: HOLDING TIME TRACKING VARIABLES
        self.position_holding_time = 0  # Current position holding time
        self.trading_frequency_history = []  # History of trading frequency
        self.peak_value = getattr(self, 'initial_asset', 100000)
        return CTAEnvV2.reset(self, is_return)
    

    def calculate_technical_indicators(self, data: np.ndarray) -> Dict[str, float]:
        """
        Calculate comprehensive technical indicators for crypto trading

        Args:
            data: OHLCV data array with columns [open, high, low, close, volume]

        Returns:
            Dictionary of calculated technical indicators
        """
        if len(data) < max(self.ta_params.values()):
            # Not enough data for calculations, return zeros
            return {col: 0.0 for col in self.feature_columns if col not in ['score']}
        # ['open', 'close', 'low', 'high', 'vol', 'val']
        close_prices = data[:, 1]  # Close prices
        high_prices = data[:, 3]   # High prices
        low_prices = data[:, 2]    # Low prices
        volumes = data[:, 4]       # Volume

        indicators = {}

        # Volatility features
        returns = np.diff(np.log(close_prices))
        indicators['volatility_5m'] = np.std(returns[-5:]) * np.sqrt(105120)
        indicators['volatility_15m'] = np.std(returns[-15:]) * np.sqrt(105120)
        indicators['volatility_60m'] = np.std(returns[-60:]) * np.sqrt(105120)
        indicators['volatility_240m'] = np.std(returns[-240:]) * np.sqrt(105120)
        indicators['realized_vol'] = np.std(returns[-self.volatility_lookback:]) * np.sqrt(525600)
        indicators['vol_ratio'] = indicators['volatility_5m'] / (indicators['volatility_240m'] + 1e-8)

        # RSI calculation
        indicators['rsi_14'] = self._calculate_rsi(close_prices, self.ta_params['rsi_period'])
        indicators['rsi_30'] = self._calculate_rsi(close_prices, 30)

        # MACD calculation
        macd_line, signal_line = self._calculate_macd(close_prices)
        indicators['macd'] = macd_line
        indicators['macd_signal'] = signal_line

        # Bollinger Bands
        bb_upper, bb_lower, bb_width = self._calculate_bollinger_bands(close_prices)
        indicators['bb_upper'] = bb_upper
        indicators['bb_lower'] = bb_lower
        indicators['bb_width'] = bb_width

        # Moving averages
        indicators['ema_12'] = self._calculate_ema(close_prices, self.ta_params['ema_short'])
        indicators['ema_26'] = self._calculate_ema(close_prices, self.ta_params['ema_long'])
        indicators['sma_50'] = self._calculate_sma(close_prices, self.ta_params['sma_period'])
        indicators['momentum_10'] = self._calculate_momentum(close_prices, self.ta_params['momentum_period'])

        # Volume features
        indicators['volume_ratio'] = volumes[-1] / (np.mean(volumes[-20:]) + 1e-8)
        indicators['volume_sma_ratio'] = np.mean(volumes[-5:]) / (np.mean(volumes[-20:]) + 1e-8)
        indicators['vwap_ratio'] = self._calculate_vwap_ratio(close_prices, volumes)
        indicators['volume_volatility'] = np.std(volumes[-20:]) / (np.mean(volumes[-20:]) + 1e-8)

        # Market microstructure (placeholder - would need real bid/ask data)
        indicators['trade_intensity'] = volumes[-1] / (np.mean(volumes[-10:]) + 1e-8)

        # Cross-timeframe features
        indicators['trend_alignment'] = self._calculate_trend_alignment(close_prices)
        indicators['support_resistance'] = self._calculate_support_resistance(close_prices, high_prices, low_prices)
        indicators['breakout_signal'] = self._calculate_breakout_signal(close_prices, high_prices, low_prices)

        return indicators
    
    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """Calculate Relative Strength Index"""
        if len(prices) < period + 1:
            return 50.0  # Neutral RSI

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_macd(self, prices: np.ndarray) -> Tuple[float, float]:
        """Calculate MACD line and signal line"""
        if len(prices) < self.ta_params['macd_slow']:
            return 0.0, 0.0

        ema_fast = self._calculate_ema(prices, self.ta_params['macd_fast'])
        ema_slow = self._calculate_ema(prices, self.ta_params['macd_slow'])
        macd_line = ema_fast - ema_slow

        # For signal line, we'd need MACD history - simplified here
        signal_line = macd_line * 0.9  # Simplified signal

        return macd_line, signal_line

    def _calculate_bollinger_bands(self, prices: np.ndarray) -> Tuple[float, float, float]:
        """Calculate Bollinger Bands"""
        if len(prices) < self.ta_params['bb_period']:
            return 0.0, 0.0, 0.0

        sma = np.mean(prices[-self.ta_params['bb_period']:])
        std = np.std(prices[-self.ta_params['bb_period']:])

        upper_band = sma + (self.ta_params['bb_std'] * std)
        lower_band = sma - (self.ta_params['bb_std'] * std)
        width = (upper_band - lower_band) / sma

        # Normalize relative to current price
        current_price = prices[-1]
        upper_norm = (upper_band - current_price) / current_price
        lower_norm = (current_price - lower_band) / current_price

        return upper_norm, lower_norm, width

    def _calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """Calculate Exponential Moving Average"""
        if len(prices) < period:
            return np.mean(prices)

        alpha = 2.0 / (period + 1)
        ema = prices[0]
        for price in prices[1:]:
            ema = alpha * price + (1 - alpha) * ema

        return (ema - prices[-1]) / prices[-1]  # Normalized

    def _calculate_sma(self, prices: np.ndarray, period: int) -> float:
        """Calculate Simple Moving Average"""
        if len(prices) < period:
            return 0.0

        sma = np.mean(prices[-period:])
        return (sma - prices[-1]) / prices[-1]  # Normalized

    def _calculate_momentum(self, prices: np.ndarray, period: int) -> float:
        """Calculate Price Momentum"""
        if len(prices) < period + 1:
            return 0.0

        return (prices[-1] - prices[-period-1]) / prices[-period-1]

    def _calculate_vwap_ratio(self, prices: np.ndarray, volumes: np.ndarray) -> float:
        """Calculate Volume Weighted Average Price ratio"""
        if len(prices) < 20 or len(volumes) < 20:
            return 0.0

        recent_prices = prices[-20:]
        recent_volumes = volumes[-20:]

        vwap = np.sum(recent_prices * recent_volumes) / np.sum(recent_volumes)
        return (prices[-1] - vwap) / vwap

    def _calculate_trend_alignment(self, prices: np.ndarray) -> float:
        """Calculate trend alignment across multiple timeframes"""
        if len(prices) < 60:
            return 0.0

        # Short, medium, long term trends
        short_trend = (prices[-1] - prices[-5]) / prices[-5]
        medium_trend = (prices[-1] - prices[-15]) / prices[-15]
        long_trend = (prices[-1] - prices[-60]) / prices[-60]

        # Alignment score: positive when all trends agree
        trends = [short_trend, medium_trend, long_trend]
        positive_trends = sum(1 for t in trends if t > 0)
        negative_trends = sum(1 for t in trends if t < 0)

        if positive_trends == 3:
            return 1.0
        elif negative_trends == 3:
            return -1.0
        else:
            return 0.0

    def _calculate_support_resistance(self, prices: np.ndarray, highs: np.ndarray, lows: np.ndarray) -> float:
        """Calculate support/resistance level proximity"""
        if len(prices) < 50:
            return 0.0

        current_price = prices[-1]
        recent_highs = highs[-50:]
        recent_lows = lows[-50:]

        # Find significant levels (simplified)
        resistance_level = np.percentile(recent_highs, 95)
        support_level = np.percentile(recent_lows, 5)

        # Distance to levels (normalized)
        resistance_dist = (resistance_level - current_price) / current_price
        support_dist = (current_price - support_level) / current_price

        # Return proximity to nearest level
        return min(resistance_dist, support_dist)

    def _calculate_breakout_signal(self, prices: np.ndarray, highs: np.ndarray, lows: np.ndarray) -> float:
        """Calculate breakout signal strength"""
        if len(prices) < 20:
            return 0.0

        current_price = prices[-1]
        recent_high = np.max(highs[-20:-1])  # Exclude current bar
        recent_low = np.min(lows[-20:-1])

        # Breakout above recent high
        if current_price > recent_high:
            return (current_price - recent_high) / recent_high
        # Breakdown below recent low
        elif current_price < recent_low:
            return (current_price - recent_low) / recent_low
        else:
            return 0.0
    
    def _calculate_regime_stability(self) -> float:
        """Calculate market regime stability indicator"""
        if len(self.returns_history) < 20:
            return 0.5  # Neutral stability

        # Calculate rolling volatility to assess regime stability
        recent_returns = np.array(self.returns_history[-20:])
        rolling_vol = []

        for i in range(5, len(recent_returns)):
            window_vol = np.std(recent_returns[i-5:i])
            rolling_vol.append(window_vol)

        if len(rolling_vol) < 2:
            return 0.5

        # Stability is inversely related to volatility of volatility
        vol_of_vol = np.std(rolling_vol)
        stability = 1.0 / (1.0 + vol_of_vol * 1000)  # Scale for numerical stability

        return np.clip(stability, 0.0, 1.0)
    
    def _estimate_current_volatility(self) -> float:
        """Estimate current market volatility"""
        if len(self.returns_history) < 10:
            return self.volatility_target

        recent_returns = self.returns_history[-20:]
        return np.std(recent_returns) * np.sqrt(525600)  # Annualized
    
    def _get_risk_management_features(self) -> List[float]:
        """Get additional risk management features for state representation"""
        features = []

        # Current drawdown level
        features.append(self.current_drawdown)

        # Position concentration (how much of portfolio is in current position)
        current_position_value = self.shares * self.cur_close
        position_concentration = current_position_value / self.total_asset if self.total_asset > 0 else 0.0
        features.append(position_concentration)

        # Recent volatility estimate
        current_vol = self._estimate_current_volatility()
        vol_normalized = current_vol / self.volatility_target
        features.append(vol_normalized)

        # Recent performance (last 10 periods)
        if len(self.returns_history) >= 10:
            recent_performance = np.mean(self.returns_history[-10:])
            features.append(recent_performance * 100)  # Scale for better numerical properties
        else:
            features.append(0.0)

        # Trading frequency (orders per period)
        if hasattr(self, 'episode_orders') and self.cur_kl > 0:
            trading_frequency = self.episode_orders / self.cur_kl
            features.append(trading_frequency)
        else:
            features.append(0.0)

        # NEW: HOLDING TIME FEATURES
        # Normalized position holding time
        normalized_holding_time = min(self.position_holding_time / (self.min_holding_periods * 2), 1.0)
        features.append(normalized_holding_time)

        # Recent trading frequency (last 20 periods)
        if len(self.trading_frequency_history) >= 10:
            recent_trading_freq = np.mean(self.trading_frequency_history[-20:])
            features.append(recent_trading_freq)
        else:
            features.append(0.0)

        # Market regime stability indicator
        regime_stability = self._calculate_regime_stability()
        features.append(regime_stability)

        return features

    def get_state(self):
        """
        Enhanced state representation for cryptocurrency trading

        Returns comprehensive state including:
        - Original features (score, price changes)
        - Technical indicators (RSI, MACD, Bollinger Bands, etc.)
        - Volatility measures
        - Volume features
        - Market microstructure indicators
        - Risk management metrics
        """
        # Get base state from parent class
        base_state = super().get_state()

        # Extract OHLCV data for technical analysis
        try:
            # Get recent OHLCV data for technical indicator calculation
            lookback_period = max(100, max(self.ta_params.values()) + 10)
            start_idx = max(0, self.cur_kl - lookback_period)
            end_idx = self.cur_kl + self.last_kl_offset + 1

            if end_idx <= len(self.data):
                ohlcv_data = self.data.iloc[start_idx:end_idx][self.olhcv_cols].values

                # Calculate technical indicators
                if len(ohlcv_data) > 0:
                    indicators = self.calculate_technical_indicators(ohlcv_data)

                    # Create enhanced state vector
                    enhanced_features = []
                    for feature in self.feature_columns:
                        if feature == 'score' or feature.startswith('pre_cr_'):
                            # Keep original price change features
                            feature_idx = self.feature_columns.index(feature)
                            if feature_idx < len(base_state):
                                enhanced_features.append(base_state[feature_idx])

                            else:
                                enhanced_features.append(0.0)
                            
                        else:
                            # Add new technical indicators
                            enhanced_features.append(indicators.get(feature, 0.0))

                    # Add risk management features
                    risk_features = self._get_risk_management_features()
                    enhanced_features.extend(risk_features)

                    # Add portfolio information (normalized capital amounts)
                    # portfolio_info = base_state[-self.info_dim:]
                    # enhanced_features.extend(portfolio_info)

                    enhanced_state = np.array(enhanced_features, dtype=np.float32)

                    # Ensure no NaN or infinite values
                    enhanced_state = np.nan_to_num(enhanced_state, nan=0.0, posinf=1.0, neginf=-1.0)

                    self.cur_state = enhanced_state
                    self.raw_state = base_state[0]
                    return enhanced_state

        except Exception as e:
            logger.warning(f"Error calculating enhanced state: {e}, falling back to base state")

        # Fallback to base state if enhancement fails
        return base_state
    
    def get_reward(self):

        cur_pos = self.cur_action
        prev_pos = self.shares * self.cur_close / self.total_asset
        prev_shares = self.shares
        diff_pos = cur_pos - prev_pos
        
        # if self.prev_state is not None and np.abs(self.cur_state[0] - self.prev_state[0]) < self.min_action:
        #     self.pos_list.append(prev_pos)
        #     self.turnover_list.append(0)
        # elif diff_pos > self.min_action:    # OPEN
        reward = 0
        if diff_pos > self.min_action:    # OPEN
            self.shares = cur_pos * self.total_asset / self.cur_close
            self.cash = (1 - cur_pos) * self.total_asset

            # adjust shares and cash
            if prev_pos >= 0:           # long, open long, cost by share
                self.shares -= (self.shares - prev_shares) * self.transaction_cost
            else:
                if cur_pos <= 0:        # short, close short, cost by cash
                    self.cash -= (self.shares - prev_shares) * self.cur_close * self.transaction_cost
                else:                   # long, close short, open long
                    self.cash -= -prev_shares * self.cur_close * self.transaction_cost
                    self.shares *= (1 - self.transaction_cost)

            self.pos_list.append(cur_pos)
            self.turnover_list.append(np.abs(diff_pos))
            self.episode_orders += 1
            self.prev_pos = cur_pos
            self.prev_state = self.cur_state
        elif diff_pos < -self.min_action:   # CLOSE
            self.shares = cur_pos * self.total_asset / self.cur_close
            self.cash = (1 - cur_pos) * self.total_asset
            if prev_pos <= 0:       # short, open short, cost by share
                self.shares += (prev_shares - self.shares) * self.transaction_cost
            else:
                if cur_pos >= 0:    # long, close long, cost by cash
                    self.cash -= (prev_shares - self.shares) * self.cur_close * self.transaction_cost
                else:               # short, close long, open short
                    self.cash -= prev_shares * self.cur_close * self.transaction_cost
                    self.shares *= (1 - self.transaction_cost)
            self.pos_list.append(cur_pos)
            self.turnover_list.append(np.abs(diff_pos))
            self.episode_orders += 1
            self.prev_pos = cur_pos
            self.prev_state = self.cur_state
        else:
            self.pos_list.append(prev_pos)
            self.turnover_list.append(0)
            reward -= self.encourage_penalty*self.total_asset
        self.next_total_asset = float(np.sum(self.next_close * self.shares) + self.cash)

        # reward += np.log(float(self.next_total_asset/self.total_asset))
        reward += self.next_total_asset - self.total_asset

        # Track returns for risk metrics
        if self.total_asset > 0:
            period_return = (self.next_total_asset - self.total_asset) / self.total_asset
            self.returns_history.append(period_return)

            # Keep only recent history for efficiency
            if len(self.returns_history) > 1000:
                self.returns_history = self.returns_history[-500:]

        # Update drawdown tracking
        if self.next_total_asset > self.peak_value:
            self.peak_value = self.next_total_asset
            self.current_drawdown = 0.0
        else:
            self.current_drawdown = (self.peak_value - self.next_total_asset) / self.peak_value

        # NEW: UPDATE HOLDING TIME TRACKING
        position_changed = abs(diff_pos) > self.min_action

        if position_changed:
            # Reset holding time when position changes
            self.position_holding_time = 0
            # Track trading frequency
            self.trading_frequency_history.append(1)
        else:
            # Increment holding time when position is maintained
            self.position_holding_time += 1
            # Track no trading
            self.trading_frequency_history.append(0)

        if self.reward_type == 'relative':
            # base_profit = np.log((prev_shares*self.next_close+self.cash)/(prev_shares*self.cur_close+self.cash))
            base_profit = prev_shares * (self.next_close - self.cur_close)
            reward -= base_profit
        if cur_pos > 0:
            self.open_reward.append(reward)
        elif cur_pos < 0:
            self.close_reward.append(reward)
        else:
            self.hold_reward.append(reward)

        if self.eval:
            if np.random.rand() < self.eval_print_ratio:
                logger.info(f'{self.cur_ts}, state={self.raw_state}, pos={cur_pos}, action={self.cur_action}, cash={self.cash}, share={self.shares}, reward={reward}, total_asset={self.total_asset}, episode_orders={self.episode_orders}')
        self.reward_list.append(reward)

        return reward


    def _norm_action(self, action):
        # action = np.clip(action, 0, 1)
        if self.action_dim > 1:
            action = action / self.action_dim
        else:
            if self.enable_short:
                action = np.clip(action[0], -1, 1)
                # action = np.clip(action[0], 0, 1)
            else:
                action = np.clip(action[0], 0, 1)
                # action = (action[0] + 1) / 2
            # action = (action + 1) / 2  # scale to [0, 1]
        return action
    

    def step(self, action):
        self.cur_kl += 1
        cur_kl = self.cur_kl+self.last_kl_offset
        self.cur_ts = self.data.iloc[cur_kl]['datatime']
        self.cur_close = self.data.iloc[cur_kl-1]['close']
        self.next_close = self.data.iloc[cur_kl]['close']
        self.cur_action = self._norm_action(action)
        self.cur_action_list.append(self.cur_action)
        
        reward = self.get_reward()
        state = self.get_state()

        self.cumulative_returns = self.next_total_asset / self.initial_asset * 100
        self.total_asset = self.next_total_asset
        self.gamma_return = float(self.gamma_return * self.gamma + reward)
        self.prev_action = self.cur_action
        
        done = (self.cur_kl >= self.max_step) or (
            float(self.total_asset) <= float(self.initial_asset * self.capital_thresh))
        if done:
            hold_ret = float(self.next_close / self.init_close)
            # reward = self.gamma_return
            # reward += (self.cumulative_returns - 100) * 100
            if self.base_return_list:
                base_ret = 100 * (np.sum(self.base_return_list) / self.initial_asset + 1)
            else:
                base_ret = 100
            # if self.cumulative_returns > base_ret:
            #     reward += 1000
            # elif self.cumulative_returns < self.capital_thresh * 100:
            #     reward += -1000
            # else:
            #     reward += -500

            # reward += self.gamma_return
            reward += 1 / (1 - self.gamma) * np.mean(self.reward_list)
            other_logging_str = ''
            if self.base_return_list:
                other_logging_str += f', base_ret={base_ret:.2f}'
            if self.cur_action_list:
                other_logging_str += f', action_avg={np.mean(self.cur_action_list):.2f}, action_std={np.std(self.cur_action_list):.2}'
            if self.pos_list:
                other_logging_str += f', pos={np.mean(self.pos_list):.2f}'
            if self.turnover_list:
                other_logging_str += f', turnover={np.mean(self.turnover_list):.2f}'
            other_logging_str += f', reward stat: min={np.min(self.reward_list):.2f}, max={np.max(self.reward_list):.2f}, avg={np.mean(self.reward_list):.2f}, std={np.std(self.reward_list):.2f}'
            other_logging_str += f', open_reward={np.sum(self.open_reward):.2f}/{len(self.open_reward)}, close_reward={np.sum(self.close_reward):.2f}/{len(self.close_reward)}, hold_reward={np.sum(self.hold_reward):.2f}/{len(self.hold_reward)}'
            if self.eval:
                logger.warning(f'{self.cur_kl}/{self.max_step}@{self.cur_ts}, {self.cur_symbol}, {int(self.total_asset)}/{int(self.initial_asset)}, cash={int(self.cash)}, share={self.shares*self.cur_close:.2f}, orders={self.episode_orders}, cum_ret={self.cumulative_returns:.2f}, {hold_ret=:.2f}, raw rewards={np.sum(self.reward_list):.2f}, final reward={reward:.2f}{other_logging_str}')
            else:
                logger.info(f'{self.cur_kl}/{self.max_step}@{self.cur_ts}, {self.cur_symbol}, {int(self.total_asset)}/{int(self.initial_asset)}, cash={int(self.cash)}, share={self.shares*self.cur_close:.2f}, orders={self.episode_orders}, cum_ret={self.cumulative_returns:.2f}, {hold_ret=:.2f}, raw rewards={np.sum(self.reward_list):.2f}, final reward={reward:.2f}{other_logging_str}')
            self.gamma_return = 0.0

        truncated = False
        return state, reward, done, truncated, {}


