import numpy as np
import pandas as pd
import os
from loguru import logger
from functorch import vmap
from copy import deepcopy
from elegantrl.envs.CTAEnvV4Pos import CTAEnvV4Pos

# CTA V4: 0528
# action=thres
class CTAEnvV4PosMax(CTAEnvV4Pos):

    env_name = 'CTAEnv-V4-Pos-Max'
    olhcv_cols = ['open', 'close', 'low', 'high', 'vol', 'val']
    cwd = f'data_snap/pred_model_5min_gain_v5_432882_online'
    ohlcv_dir = f'data_snap/portfolio_v1'
    feature_columns = [
        'score', 'pre_cr_1', 'pre_cr_3', 'pre_cr_5', 'pre_cr_15', 'pre_cr_30', 'pre_cr_60'
    ]
    score_base = 0.1
    eval_print_ratio = 0.00001
    norm_nums = len(feature_columns)+4 # state中记录持有量
    # info = (norm_usd_capital, norm_crypto_capital, crypto_to_capital_ratio) 
    info_dim = 0
    perf_fea_dims = 4
    pos_frac = 5

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.score_return_history = []
        self.order_returns_history = []
        self.state_dim = self.fea_dim + self.info_dim + self.perf_fea_dims

    def reset(self, is_return=True):
        self.score_return_history = []
        self.order_returns_history = []
        return CTAEnvV4Pos.reset(self, is_return)


    def _get_performance_features(self):
        features = []
        # Recent score performance
        if len(self.score_return_history) >= 10:
            recent_performance = np.mean(self.score_return_history[-10:])
            features.append(recent_performance * 100)  # Scale for better numerical properties
            recent_performance = np.std(self.score_return_history[-10:]*100)
            features.append(recent_performance)
        else:
            features.append(0.0)
            features.append(0.0)

        # Recent agent performance (last 10 open)
        if len(self.order_returns_history) >= 10:
            recent_performance = np.mean(self.order_returns_history[-10:])
            features.append(recent_performance * 100)  # Scale for better numerical properties
            recent_performance = np.std(self.order_returns_history[-10:]*100)
            features.append(recent_performance)
        else:
            features.append(0.0)
            features.append(0.0)
        print(features)
        return features
    
    def get_state(self):
        state = super().get_state()
        performance_features = self._get_performance_features()
        state = np.hstack((state, performance_features))
        self.raw_state = state
        self.cur_state = state
        return state
    
    def get_reward(self):
        base_return = 0
        oracle_return = 0
        score = self.raw_state[0]
        self.score_list.append(score)
        sell_close = self.sell_close
        possible_profit_with_cost = sell_close * (1-self.transaction_cost)**2 - self.cur_close
        possible_profit_without_cost = sell_close * (1-self.transaction_cost) - self.cur_close
        # base by score
        if score > self.score_thres:
            # raw_open_cap = max_open_capital * max(self.min_action, (score - self.score_base))
            raw_open_cap = self.max_open_capital * (score - self.score_base)
            # raw_open_cap = min(self.cash, self.max_open_capital) * (score - self.score_base)
            buy_volume = raw_open_cap / self.cur_close
            # wait_to_buy_volume = self.clean_position(buy_volume, dry_run=True)
            wait_to_buy_volume = buy_volume
            base_return = possible_profit_with_cost * wait_to_buy_volume + \
                    possible_profit_without_cost * (buy_volume - wait_to_buy_volume)
            self.score_return_history.append(base_return/raw_open_cap)
        
        # base by oracle
        buy_volume = self.max_open_capital / self.cur_close
        # buy_volume = self.max_open_capital / self.cur_close
        # wait_to_buy_volume = self.clean_position(buy_volume, dry_run=True)
        wait_to_buy_volume = buy_volume
        oracle_return = possible_profit_with_cost * wait_to_buy_volume + \
                possible_profit_without_cost * (buy_volume - wait_to_buy_volume)
        if oracle_return < 0: oracle_return = 0
        
        self.base_return_list.append(base_return)
        self.oracle_return_list.append(max(0, oracle_return))
        encourage_penalty = 0.15/365/24/60 * self.total_asset #1e-5 * self.total_asset

        reward = 0
        # OPEN
        if self.cur_action >= self.min_action and score >= self.open_thres:
            self.stat_info['open'] += 1
            cur_action = np.clip(self.cur_action, 0, 1)
            open_cap = self.max_open_capital * cur_action
            buy_volume = open_cap / self.cur_close
            wait_to_buy_volume = self.clean_position(buy_volume)
            if wait_to_buy_volume > 0:
                if wait_to_buy_volume * self.cur_close > self.cash:
                    buy_volume -= wait_to_buy_volume
                    wait_to_buy_volume = self.cash / self.cur_close
                    buy_volume += wait_to_buy_volume
                self.cash -= wait_to_buy_volume * self.cur_close
                self.episode_orders += 1
                self.shares += wait_to_buy_volume * (1 - self.transaction_cost)
            if buy_volume > 0:
                real_buy_volume = buy_volume - wait_to_buy_volume * self.transaction_cost
                self.positon.append((self.cur_ts, real_buy_volume))

            reward += possible_profit_with_cost * wait_to_buy_volume + (buy_volume - wait_to_buy_volume) * possible_profit_without_cost
            self.order_returns_history.append(reward/open_cap)
            if self.reward_base == 'model':
                reward -= base_return
            elif self.reward_base == 'oracle':
                reward -= oracle_return
        # HOLD
        else:
            self.stat_info['hold'] += 1
            if self.reward_base == 'model' and score >= self.open_thres:
                reward -= base_return
            elif self.reward_base == 'oracle' and score >= self.open_thres:
                reward -= oracle_return
            # reward -= encourage_penalty * self.total_asset
            _ = self.clean_position()

        if len(self.positon) == 0:
            self.shares = 0.0
        self.next_total_asset = float(np.sum(self.next_close * self.shares) + self.cash)
        if self.reward_type == 'asset':
            # reward = np.log(float(self.next_total_asset/self.total_asset))
            reward = float(self.next_total_asset - self.total_asset)
        elif self.reward_type == 'sharpe':
            reward = 0
            # Track returns for risk metrics
            period_return = (self.next_total_asset - self.total_asset) / self.total_asset
            self.returns_history.append(period_return)
            if len(self.returns_history) >= self.sharpe_windows:
                returns_array = np.array(self.returns_history[-self.sharpe_windows:])
                if np.std(returns_array) > 0:
                    sharpe_ratio = (np.mean(returns_array) - self.risk_free_rate / 525600) / np.std(returns_array)
                    reward = sharpe_ratio
        # reward -= encourage_penalty
        if self.cur_action >= self.min_action:# and score >= self.filter_thres:
            self.open_reward.append(reward)
        else:
            # if len(self.positon) == 0:
            #     reward -= min(encourage_penalty, float(oracle_return))
            # if oracle_return > 0:
            #     reward -= min(encourage_penalty, float(oracle_return))
            self.hold_reward.append(reward)
        if self.eval and np.random.rand() < self.eval_print_ratio:
            logger.info(f'{self.cur_ts}, state={score}/{self.cur_state}, action={self.cur_action}, cash={self.cash}, share={self.shares}, reward={reward}, total_asset={self.total_asset}, episode_orders={self.episode_orders}')
        self.reward_list.append(reward)

        return reward
    