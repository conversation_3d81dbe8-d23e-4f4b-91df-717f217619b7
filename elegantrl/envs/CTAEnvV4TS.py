import numpy as np
import pandas as pd
import os
from loguru import logger
from functorch import vmap
from copy import deepcopy
from elegantrl.envs.CTAEnvV2 import CTAEnvV2

# CTA V4: 0528
# action=thres
class CTAEnvV4TS(CTAEnvV2):

    env_name = 'CTAEnv-V4-TS'
    olhcv_cols = ['open', 'close', 'low', 'high', 'vol', 'val']
    cwd = f'data_snap/pred_model_5min_gain_v5_432882_online'
    ohlcv_dir = f'data_snap/portfolio_v1'
    feature_columns = [
        'score', 'pre_cr_1', 'pre_cr_3', 'pre_cr_5', 'pre_cr_15', 'pre_cr_30', 'pre_cr_60'
    ]
    score_thres = 0.4
    score_base = 0.1
    eval_print_ratio = 0.0001
    norm_nums = len(feature_columns) # state中记录持有量
    # info = (norm_usd_capital, norm_crypto_capital, crypto_to_capital_ratio) 
    info_dim = 2

    pos_frac = 5

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.cur_a = 0.0
        self.cur_b = 0.0
        self.cur_a_list = []
        self.cur_b_list = []
        self.reward_type = kwargs.get('reward_type', '')
        self.dry_run = kwargs.get('dry_run', False)
        if self.reward_type == '':
            self.reward_type = 'profit'
        logger.info(f'set {self.reward_type=}')
        self.min_action = kwargs.get('min_action', 0.2)

    def reset(self, is_return=True):
      return CTAEnvV2.reset(self, is_return)

    def get_reward(self):
        base_return = 0
        oracle_return = 0
        score = self.raw_state[0]
        self.score_list.append(score)
        sell_close = self.sell_close
        possible_profit_with_cost = sell_close * (1-self.transaction_cost)**2 - self.cur_close
        possible_profit_without_cost = sell_close * (1-self.transaction_cost) - self.cur_close
        # base by score
        if score > self.score_thres:
            # raw_open_cap = max_open_capital * max(self.min_action, (score - self.score_base))
            raw_open_cap = self.max_open_capital * (score - self.score_base)
            # raw_open_cap = min(self.cash, self.max_open_capital) * (score - self.score_base)
            buy_volume = raw_open_cap / self.cur_close
            # wait_to_buy_volume = self.clean_position(buy_volume, dry_run=True)
            wait_to_buy_volume = buy_volume
            base_return = possible_profit_with_cost * wait_to_buy_volume + \
                    possible_profit_without_cost * (buy_volume - wait_to_buy_volume)
        
        # base by oracle
        buy_volume = self.max_open_capital / self.cur_close
        # buy_volume = self.max_open_capital / self.cur_close
        # wait_to_buy_volume = self.clean_position(buy_volume, dry_run=True)
        wait_to_buy_volume = buy_volume
        oracle_return = possible_profit_with_cost * wait_to_buy_volume + \
                possible_profit_without_cost * (buy_volume - wait_to_buy_volume)
        if oracle_return < 0: oracle_return = 0
        
        self.base_return_list.append(base_return)
        self.oracle_return_list.append(max(0, oracle_return))
        encourage_penalty = 0.15/365/24/60 * self.total_asset #1e-5 * self.total_asset

        # action_flag = score - self.cur_a
        self.cur_action = self.cur_a * score + self.cur_b
        self.cur_action_list.append(self.cur_action)

        reward = 0
        # OPEN
        if self.cur_action >= self.min_action:# and score >= self.score_thres:
            self.stat_info['open'] += 1
            cur_action = np.clip(self.cur_action, 0, 1)
            open_cap = self.max_open_capital * cur_action
            buy_volume = open_cap / self.cur_close
            wait_to_buy_volume = self.clean_position(buy_volume)
            if wait_to_buy_volume > 0:
                if wait_to_buy_volume * self.cur_close > self.cash:
                    buy_volume -= wait_to_buy_volume
                    wait_to_buy_volume = self.cash / self.cur_close
                    buy_volume += wait_to_buy_volume
                self.cash -= wait_to_buy_volume * self.cur_close
                self.episode_orders += 1
                self.shares += wait_to_buy_volume * (1 - self.transaction_cost)
            if buy_volume > 0:
                real_buy_volume = buy_volume - wait_to_buy_volume * self.transaction_cost
                self.positon.append((self.cur_ts, real_buy_volume))

            reward += possible_profit_with_cost * wait_to_buy_volume + (buy_volume - wait_to_buy_volume) * possible_profit_without_cost
            reward -= base_return
            # reward -= oracle_return
        # HOLD
        else:
            self.stat_info['hold'] += 1
            # reward -= oracle_return
            reward -= base_return
            _ = self.clean_position()

        if len(self.positon) == 0:
            self.shares = 0.0
        self.next_total_asset = float(np.sum(self.next_close * self.shares) + self.cash)
        if self.reward_type == 'asset':
            # reward = np.log(float(self.next_total_asset/self.total_asset))
            reward = float(self.next_total_asset - self.total_asset)
        # reward -= encourage_penalty
        if self.cur_action >= self.min_action:# and score >= self.filter_thres:
            self.open_reward.append(reward)
        else:
            # if len(self.positon) == 0:
            #     reward -= min(encourage_penalty, float(oracle_return))
            # if oracle_return > 0:
            #     reward -= min(encourage_penalty, float(oracle_return))
            self.hold_reward.append(reward)
        if self.eval and np.random.rand() < self.eval_print_ratio:
            logger.info(f'{self.cur_ts}, state={score}/{self.cur_state}, params={self.cur_a}, {self.cur_b}, action={self.cur_action}, cash={self.cash}, share={self.shares}, reward={reward}, total_asset={self.total_asset}, episode_orders={self.episode_orders}')
        self.reward_list.append(reward)

        return reward
    
    def _norm_action(self, action):
        # score = a * (score + b)
        # a: amp, avg=1, 
        # b: thres, avg=thres
        if self.dry_run:
            a = 1
            b = -self.score_base
        else:
            a = action[0] + 1
            b = action[1] - self.score_base # scale avg to self.score_base

        # a = action[0] + self.score_thres   # scale to [0, 2]
        # b = action[1] + self.score_base # scale avg to self.score_base
        # action = (action + 1) / 2  # scale to [0, 1]
        return a, b
    
    def step(self, action):
        self.cur_kl += 1
        cur_kl = self.cur_kl+self.last_kl_offset
        self.cur_ts = self.data.iloc[cur_kl]['datatime']
        self.cur_close = self.data.iloc[cur_kl-1]['close']
        self.next_close = self.data.iloc[cur_kl]['close']
        self.sell_close = self.data.iloc[cur_kl+self.forward_kl_nums-1]['close']

        # ===========> V4 <==============
        self.cur_a, self.cur_b = self._norm_action(action)
        self.cur_a_list.append(self.cur_a)
        self.cur_b_list.append(self.cur_b)
        # ===========> V4 <==============

        reward = self.get_reward()
        state = self.get_state()
        self.total_asset = self.next_total_asset
        self.cumulative_returns = self.total_asset / self.initial_asset * 100
        self.gamma_return = float(self.gamma_return * self.gamma + reward)
        self.prev_action = self.cur_action
        done = (self.cur_kl >= self.max_step) or (
            float(self.total_asset) <= float(self.initial_asset * self.capital_thresh))
        if done:

            self.total_asset = float(np.sum(self.sell_close * self.shares) + self.cash)
            self.cumulative_returns = self.total_asset / self.initial_asset * 100
            hold_ret = float(self.next_close / self.init_close)
            # reward = self.gamma_return
            # reward += (self.cumulative_returns - 100) * 100
            if self.base_return_list:
                base_ret = 100 * (np.sum(self.base_return_list) / self.initial_asset + 1)
            else:
                base_ret = 100
            if self.oracle_return_list:
                oracle_ret = 100 * (np.sum(self.oracle_return_list) / self.initial_asset + 1)
            else:
                oracle_ret = 100

            reward += 1 / (1 - self.gamma) * np.mean(self.reward_list)
            other_logging_str = ''
            if self.base_return_list:
                other_logging_str += f', base_ret={base_ret:.2f}'
            if self.oracle_return_list:
                other_logging_str += f', oracle_ret={oracle_ret:.2f}'
            if self.cur_action_list:
                other_logging_str += f', action_avg={np.mean(self.cur_action_list):.2f}, action_std={np.std(self.cur_action_list):.2}'
            if self.score_list:
                other_logging_str += f', score>{self.score_thres:.2f}: {np.sum(np.array(self.score_list)>self.score_thres)}'
            # other_logging_str += f', reward stat: min={np.min(self.reward_list):.2f}, max={np.max(self.reward_list):.2f}, avg={np.mean(self.reward_list):.2f}, std={np.std(self.reward_list):.2f}, {self.stat_info}'
            other_logging_str += f', reward stat: >0= {np.sum(np.array(self.reward_list)>0):.2f}, 0={np.sum(np.array(self.reward_list)==0):.2f}, <0={np.sum(np.array(self.reward_list)<0):.2f}, avg={np.mean(self.reward_list):.2f}, std={np.std(self.reward_list):.2f}'
            other_logging_str += f', hold_reward={np.mean(self.hold_reward):.2f}, open_reward={np.mean(self.open_reward):.2f}'
            # ===========> V4 <==============
            other_logging_str += f', cur_a_avg={np.mean(self.cur_a_list):.2f}, cur_a_std={np.std(self.cur_a_list):.2f}'
            other_logging_str += f', cur_b_avg={np.mean(self.cur_b_list):.2f}, cur_b_std={np.std(self.cur_b_list):.2f}'
            # ===========> V4 <==============
            other_logging_str += f', {self.stat_info}'

            if self.eval:
                logger.warning(f'{self.cur_kl}/{self.max_step}@{self.cur_ts}, {self.cur_symbol}, {int(self.total_asset)}/{int(self.initial_asset)}, cash={int(self.cash)}, share={self.shares:.2f}, orders={self.episode_orders}, cum_ret={self.cumulative_returns:.2f}, {hold_ret=:.2f}, raw rewards={np.sum(self.reward_list):.2f}, final reward={reward:.2f}{other_logging_str}, {self.gamma_return=}')
            else:
                logger.info(f'{self.cur_kl}/{self.max_step}@{self.cur_ts}, {self.cur_symbol}, {int(self.total_asset)}/{int(self.initial_asset)}, cash={int(self.cash)}, share={self.shares:.2f}, orders={self.episode_orders}, cum_ret={self.cumulative_returns:.2f}, {hold_ret=:.2f}, raw rewards={np.sum(self.reward_list):.2f}, final reward={reward:.2f}{other_logging_str}, {self.gamma_return=}')
            self.gamma_return = 0.0

        truncated = False
        return state, reward, done, truncated, {}

