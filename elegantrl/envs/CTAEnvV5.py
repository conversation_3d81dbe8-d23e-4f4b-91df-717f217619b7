import numpy as np
import pandas as pd
from loguru import logger
from typing import Dict, List, Tuple, Optional
from .CTAEnvV3 import CTAEnvV3

class CTAEnvV5(CTAEnvV3):
    """
    Optimized CTA Environment V5 for Cryptocurrency Trading

    Key Optimizations for Crypto:
    1. Enhanced state representation with crypto-specific technical indicators
    2. Sophisticated reward function with risk-adjusted returns
    3. Advanced transaction cost modeling (maker/taker fees, slippage)
    4. Dynamic position sizing with volatility-based risk management
    5. Multi-timeframe feature engineering
    6. Market microstructure features
    """

    env_name = 'CTAEnv-V5-Crypto'

    # Enhanced feature columns for crypto trading
    feature_columns = [
        # Price-based features
        'score', 'pre_cr_1', 'pre_cr_3', 'pre_cr_5', 'pre_cr_15', 'pre_cr_30', 'pre_cr_60',

        # Volatility features
        'volatility_1m', 'volatility_5m', 'volatility_15m', 'volatility_60m',
        'realized_vol', 'vol_ratio',

        # Technical indicators
        'rsi_14', 'rsi_30', 'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'bb_width',
        'ema_12', 'ema_26', 'sma_50', 'momentum_10',

        # Volume features
        'volume_ratio', 'volume_sma_ratio', 'vwap_ratio', 'volume_volatility',

        # Market microstructure (if available)
        'bid_ask_spread', 'order_imbalance', 'trade_intensity',

        # Cross-timeframe features
        'trend_alignment', 'support_resistance', 'breakout_signal'
    ]

    info_dim = 10  # Updated: 5 original + 5 new holding time features
    
    def __init__(self, **kwargs):
        # Crypto-specific parameters
        self.volatility_lookback = kwargs.get('volatility_lookback', 20)
        self.risk_free_rate = kwargs.get('risk_free_rate', 0.02)  # 2% annual
        self.max_drawdown_threshold = kwargs.get('max_drawdown_threshold', 0.15)  # 15%
        self.volatility_target = kwargs.get('volatility_target', 0.40)  # 20% annual

        # OPTIMIZED TRANSACTION COST PARAMETERS FOR LONGER HOLDING
        self.maker_fee = kwargs.get('maker_fee', 0.0002)  # 0.2% maker fee (10x increase)
        self.taker_fee = kwargs.get('taker_fee', 0.0003)  # 0.3% taker fee (10x increase)
        self.slippage_impact = kwargs.get('slippage_impact', 0.0001)  # 0.1% base slippage (10x increase)
        self.latency_cost = kwargs.get('latency_cost', 0.0)  # 0.02% latency cost

        # Risk management parameters
        self.max_position_size = kwargs.get('max_position_size', 0.95)  # 95% max position
        self.min_position_size = kwargs.get('min_position_size', 0.01)  # 1% min position
        self.position_decay_factor = kwargs.get('position_decay_factor', 0.99)  # Position decay

        # OPTIMIZED REWARD FUNCTION PARAMETERS FOR LONGER HOLDING
        self.sharpe_weight = kwargs.get('sharpe_weight', 0)# 0.25)  # Reduced from 0.3
        self.drawdown_penalty = kwargs.get('drawdown_penalty', 0) #1.5)  # Reduced from 2.0
        self.turnover_penalty = kwargs.get('turnover_penalty', 0) #3.0)  # Increased from 0.1 (30x increase)
        self.consistency_bonus = kwargs.get('consistency_bonus', 0) #0.5)  # Increased from 0.2

        # NEW: HOLDING TIME INCENTIVE PARAMETERS
        self.holding_time_bonus = kwargs.get('holding_time_bonus', 0)#0.4)  # Bonus for holding positions
        self.min_holding_periods = kwargs.get('min_holding_periods', 5)  # Minimum periods to hold
        self.trading_frequency_penalty = kwargs.get('trading_frequency_penalty', 2.0)  # Penalty for frequent trading
        self.position_momentum_bonus = kwargs.get('position_momentum_bonus', 0)#0.3)  # Bonus for position consistency
        self.regime_stability_weight = kwargs.get('regime_stability_weight', 0.2)  # Weight for regime stability

        # OPTIMIZED SAMPLING PARAMETERS FOR LONG-ONLY EFFICIENCY
        self.enable_adaptive_sampling = kwargs.get('enable_adaptive_sampling', True)  # Enable smart sampling
        self.min_positive_samples = kwargs.get('min_positive_samples', 3)  # Reduced from 10 to 3
        self.positive_sample_weight = kwargs.get('positive_sample_weight', 2.0)  # Weight for positive samples
        self.sample_balance_ratio = kwargs.get('sample_balance_ratio', 0.3)  # Target 30% positive samples
        self.adaptive_threshold_factor = kwargs.get('adaptive_threshold_factor', 0.9)  # Adaptive threshold multiplier

        # OPTIMIZED ACTION THRESHOLD FOR LONGER HOLDING
        kwargs['min_action'] = kwargs.get('min_action', 0.15)  # Increased from 0.05 to 0.15 (3x increase)
        kwargs['encourage_penalty'] = kwargs.get('encourage_penalty', 0.001)  # Small penalty for holding

        # Initialize tracking variables BEFORE calling super().__init__
        self.returns_history = []
        self.drawdown_history = []
        self.volatility_history = []
        self.peak_value = kwargs.get('initial_asset', 100000)
        self.current_drawdown = 0.0

        # NEW: HOLDING TIME TRACKING VARIABLES
        self.position_holding_time = 0  # Current position holding time
        self.last_position_change_time = 0  # Time since last position change
        self.trading_frequency_history = []  # History of trading frequency
        self.position_stability_score = 1.0  # Score for position stability
        self.consecutive_holds = 0  # Consecutive periods without trading
        self.recent_trades_count = 0  # Number of trades in recent window

        # NEW: ADAPTIVE SAMPLING TRACKING VARIABLES
        self.symbol_positive_ratios = {}  # Track positive sample ratios per symbol
        self.symbol_performance_history = {}  # Track performance per symbol
        self.episode_positive_samples = 0  # Count of positive samples in current episode
        self.episode_total_samples = 0  # Total samples in current episode

        super().__init__(**kwargs)

        # Technical indicator calculation parameters
        self.ta_params = {
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'bb_period': 20,
            'bb_std': 2.0,
            'ema_short': 12,
            'ema_long': 26,
            'sma_period': 50,
            'momentum_period': 10
        }

        logger.info(f'Initialized {self.env_name} with enhanced crypto features')
        logger.info(f'Feature dimensions: {len(self.feature_columns)} features')
        logger.info(f'Risk management: max_pos={self.max_position_size}, drawdown_threshold={self.max_drawdown_threshold}')
        logger.info(f'Adaptive sampling: enabled={self.enable_adaptive_sampling}, min_positive={self.min_positive_samples}')

    def reset(self, is_return=True):
        """Enhanced reset with adaptive sampling for improved efficiency"""
        if self.enable_adaptive_sampling:
            # Use adaptive symbol selection and episode filtering
            max_attempts = len(self.symbols) * 3  # Limit attempts to avoid infinite loops
            attempts = 0

            while attempts < max_attempts:
                # Adaptive symbol selection
                selected_symbol = self._adaptive_symbol_selection()

                try:
                    # Load data for selected symbol (use parent class method)
                    self.cur_symbol = selected_symbol
                    symbol_data = super()._load_data(selected_symbol)

                    # Apply adaptive filtering
                    if self._adaptive_episode_filtering(symbol_data, selected_symbol):
                        # Augment positive samples if needed
                        symbol_data = self._augment_positive_samples(symbol_data)

                        # Set the selected symbol and data
                        self.cur_symbol = selected_symbol
                        self.data = symbol_data

                        # Update symbol statistics
                        score_threshold = getattr(self, 'score_thres_dict', {}).get(selected_symbol, 0.4)
                        positive_count = np.sum(symbol_data['score'] > score_threshold)
                        self.episode_positive_samples = positive_count
                        self.episode_total_samples = len(symbol_data)

                        break

                except Exception as e:
                    logger.warning(f"Error loading data for {selected_symbol}: {e}")

                attempts += 1

            if attempts >= max_attempts:
                logger.warning("Adaptive sampling failed, falling back to parent reset")
                result = super().reset(is_return)
            else:
                # Continue with standard reset process using selected data
                result = self._complete_reset_process(is_return)
        else:
            # Use parent class reset
            result = super().reset(is_return)

        # Reset crypto-specific tracking
        self.returns_history = []
        self.drawdown_history = []
        self.volatility_history = []
        self.peak_value = getattr(self, 'initial_asset', 100000)
        self.current_drawdown = 0.0

        # Reset holding time tracking variables
        self.position_holding_time = 0
        self.last_position_change_time = 0
        self.trading_frequency_history = []
        self.position_stability_score = 1.0
        self.consecutive_holds = 0
        self.recent_trades_count = 0

        return result

    def _complete_reset_process(self, is_return=True):
        """Complete the reset process with already selected data"""
        # This method handles the reset process after symbol and data selection
        # Similar to parent reset but without the symbol selection loop

        # Set score threshold for selected symbol
        if hasattr(self, 'score_thres_dict') and self.cur_symbol in self.score_thres_dict:
            self.score_thres = self.score_thres_dict[self.cur_symbol]

        # Apply data slicing if needed (for multi-worker environments)
        slice_idx = self._gen_data_slice_idx() if hasattr(self, '_gen_data_slice_idx') else None

        if slice_idx:
            start_idx, end_idx = slice_idx
            self.data = self.data.iloc[start_idx:end_idx]

        # Initialize episode parameters
        self.start_kl = 0
        self.max_step = len(self.data) - getattr(self, 'forward_kl_nums', 1) - getattr(self, 'seq_len', 1) * getattr(self, 'fea_freqs', 1)
        self.cash = getattr(self, 'initial_asset_base', self.initial_asset)
        self.shares = 0

        # Handle random reset if enabled
        if getattr(self, 'random_reset', False):
            min_kl_nums = getattr(self, 'min_kl_nums', 100)
            if self.max_step - min_kl_nums > 0:
                self.start_kl = max(0, np.random.randint(0, self.max_step - min_kl_nums))
            self.data = self.data.iloc[self.start_kl:]
            self.max_step = len(self.data) - getattr(self, 'forward_kl_nums', 1) - getattr(self, 'seq_len', 1) * getattr(self, 'fea_freqs', 1)

            # Randomize initial asset
            self.initial_asset = getattr(self, 'initial_asset_base', self.initial_asset) * np.random.uniform(0.5, 1.5)
            self.max_open_capital = self.initial_asset / getattr(self, 'pos_frac', 5)
            self.cash = self.initial_asset - self.shares * self.data.iloc[getattr(self, 'last_kl_offset', 0)]['close']

        # Initialize episode state
        self.max_step = min(self.max_step, getattr(self, 'env_max_step', float('inf')))
        self.cur_kl = 0
        self.cur_action = 0
        self.cur_state = None
        self.prev_action = 0
        self.gamma_return = 0.0
        self.cumulative_returns = 0.0
        self.episode_orders = 0

        # Initialize tracking lists
        self.reward_list = []
        self.open_reward = []
        self.hold_reward = []
        self.close_reward = []

        if is_return:
            return self.get_state()
        else:
            self.get_state()
            return None

    def calculate_technical_indicators(self, data: np.ndarray) -> Dict[str, float]:
        """
        Calculate comprehensive technical indicators for crypto trading

        Args:
            data: OHLCV data array with columns [open, high, low, close, volume]

        Returns:
            Dictionary of calculated technical indicators
        """
        if len(data) < max(self.ta_params.values()):
            # Not enough data for calculations, return zeros
            return {col: 0.0 for col in self.feature_columns if col not in ['score']}

        close_prices = data[:, 3]  # Close prices
        high_prices = data[:, 1]   # High prices
        low_prices = data[:, 2]    # Low prices
        volumes = data[:, 4]       # Volume

        indicators = {}

        # Volatility features
        returns = np.diff(np.log(close_prices))
        indicators['volatility_1m'] = np.std(returns[-1:]) * np.sqrt(525600)  # Annualized
        indicators['volatility_5m'] = np.std(returns[-5:]) * np.sqrt(105120)
        indicators['volatility_15m'] = np.std(returns[-15:]) * np.sqrt(35040)
        indicators['volatility_60m'] = np.std(returns[-60:]) * np.sqrt(8760)
        indicators['realized_vol'] = np.std(returns[-self.volatility_lookback:]) * np.sqrt(525600)
        indicators['vol_ratio'] = indicators['volatility_1m'] / (indicators['volatility_60m'] + 1e-8)

        # RSI calculation
        indicators['rsi_14'] = self._calculate_rsi(close_prices, self.ta_params['rsi_period'])
        indicators['rsi_30'] = self._calculate_rsi(close_prices, 30)

        # MACD calculation
        macd_line, signal_line = self._calculate_macd(close_prices)
        indicators['macd'] = macd_line
        indicators['macd_signal'] = signal_line

        # Bollinger Bands
        bb_upper, bb_lower, bb_width = self._calculate_bollinger_bands(close_prices)
        indicators['bb_upper'] = bb_upper
        indicators['bb_lower'] = bb_lower
        indicators['bb_width'] = bb_width

        # Moving averages
        indicators['ema_12'] = self._calculate_ema(close_prices, self.ta_params['ema_short'])
        indicators['ema_26'] = self._calculate_ema(close_prices, self.ta_params['ema_long'])
        indicators['sma_50'] = self._calculate_sma(close_prices, self.ta_params['sma_period'])
        indicators['momentum_10'] = self._calculate_momentum(close_prices, self.ta_params['momentum_period'])

        # Volume features
        indicators['volume_ratio'] = volumes[-1] / (np.mean(volumes[-20:]) + 1e-8)
        indicators['volume_sma_ratio'] = np.mean(volumes[-5:]) / (np.mean(volumes[-20:]) + 1e-8)
        indicators['vwap_ratio'] = self._calculate_vwap_ratio(close_prices, volumes)
        indicators['volume_volatility'] = np.std(volumes[-20:]) / (np.mean(volumes[-20:]) + 1e-8)

        # Market microstructure (placeholder - would need real bid/ask data)
        indicators['bid_ask_spread'] = 0.001  # Placeholder
        indicators['order_imbalance'] = 0.0   # Placeholder
        indicators['trade_intensity'] = volumes[-1] / (np.mean(volumes[-10:]) + 1e-8)

        # Cross-timeframe features
        indicators['trend_alignment'] = self._calculate_trend_alignment(close_prices)
        indicators['support_resistance'] = self._calculate_support_resistance(close_prices, high_prices, low_prices)
        indicators['breakout_signal'] = self._calculate_breakout_signal(close_prices, high_prices, low_prices)

        return indicators

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """Calculate Relative Strength Index"""
        if len(prices) < period + 1:
            return 50.0  # Neutral RSI

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_macd(self, prices: np.ndarray) -> Tuple[float, float]:
        """Calculate MACD line and signal line"""
        if len(prices) < self.ta_params['macd_slow']:
            return 0.0, 0.0

        ema_fast = self._calculate_ema(prices, self.ta_params['macd_fast'])
        ema_slow = self._calculate_ema(prices, self.ta_params['macd_slow'])
        macd_line = ema_fast - ema_slow

        # For signal line, we'd need MACD history - simplified here
        signal_line = macd_line * 0.9  # Simplified signal

        return macd_line, signal_line

    def _calculate_bollinger_bands(self, prices: np.ndarray) -> Tuple[float, float, float]:
        """Calculate Bollinger Bands"""
        if len(prices) < self.ta_params['bb_period']:
            return 0.0, 0.0, 0.0

        sma = np.mean(prices[-self.ta_params['bb_period']:])
        std = np.std(prices[-self.ta_params['bb_period']:])

        upper_band = sma + (self.ta_params['bb_std'] * std)
        lower_band = sma - (self.ta_params['bb_std'] * std)
        width = (upper_band - lower_band) / sma

        # Normalize relative to current price
        current_price = prices[-1]
        upper_norm = (upper_band - current_price) / current_price
        lower_norm = (current_price - lower_band) / current_price

        return upper_norm, lower_norm, width

    def _calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """Calculate Exponential Moving Average"""
        if len(prices) < period:
            return np.mean(prices)

        alpha = 2.0 / (period + 1)
        ema = prices[0]
        for price in prices[1:]:
            ema = alpha * price + (1 - alpha) * ema

        return (ema - prices[-1]) / prices[-1]  # Normalized

    def _calculate_sma(self, prices: np.ndarray, period: int) -> float:
        """Calculate Simple Moving Average"""
        if len(prices) < period:
            return 0.0

        sma = np.mean(prices[-period:])
        return (sma - prices[-1]) / prices[-1]  # Normalized

    def _calculate_momentum(self, prices: np.ndarray, period: int) -> float:
        """Calculate Price Momentum"""
        if len(prices) < period + 1:
            return 0.0

        return (prices[-1] - prices[-period-1]) / prices[-period-1]

    def _calculate_vwap_ratio(self, prices: np.ndarray, volumes: np.ndarray) -> float:
        """Calculate Volume Weighted Average Price ratio"""
        if len(prices) < 20 or len(volumes) < 20:
            return 0.0

        recent_prices = prices[-20:]
        recent_volumes = volumes[-20:]

        vwap = np.sum(recent_prices * recent_volumes) / np.sum(recent_volumes)
        return (prices[-1] - vwap) / vwap

    def _calculate_trend_alignment(self, prices: np.ndarray) -> float:
        """Calculate trend alignment across multiple timeframes"""
        if len(prices) < 60:
            return 0.0

        # Short, medium, long term trends
        short_trend = (prices[-1] - prices[-5]) / prices[-5]
        medium_trend = (prices[-1] - prices[-15]) / prices[-15]
        long_trend = (prices[-1] - prices[-60]) / prices[-60]

        # Alignment score: positive when all trends agree
        trends = [short_trend, medium_trend, long_trend]
        positive_trends = sum(1 for t in trends if t > 0)
        negative_trends = sum(1 for t in trends if t < 0)

        if positive_trends == 3:
            return 1.0
        elif negative_trends == 3:
            return -1.0
        else:
            return 0.0

    def _calculate_support_resistance(self, prices: np.ndarray, highs: np.ndarray, lows: np.ndarray) -> float:
        """Calculate support/resistance level proximity"""
        if len(prices) < 50:
            return 0.0

        current_price = prices[-1]
        recent_highs = highs[-50:]
        recent_lows = lows[-50:]

        # Find significant levels (simplified)
        resistance_level = np.percentile(recent_highs, 95)
        support_level = np.percentile(recent_lows, 5)

        # Distance to levels (normalized)
        resistance_dist = (resistance_level - current_price) / current_price
        support_dist = (current_price - support_level) / current_price

        # Return proximity to nearest level
        return min(resistance_dist, support_dist)

    def _calculate_breakout_signal(self, prices: np.ndarray, highs: np.ndarray, lows: np.ndarray) -> float:
        """Calculate breakout signal strength"""
        if len(prices) < 20:
            return 0.0

        current_price = prices[-1]
        recent_high = np.max(highs[-20:-1])  # Exclude current bar
        recent_low = np.min(lows[-20:-1])

        # Breakout above recent high
        if current_price > recent_high:
            return (current_price - recent_high) / recent_high
        # Breakdown below recent low
        elif current_price < recent_low:
            return (current_price - recent_low) / recent_low
        else:
            return 0.0

    def calculate_enhanced_transaction_costs(self, position_change: float, market_impact: float = 1.0) -> float:
        """
        Calculate realistic transaction costs for crypto trading

        Args:
            position_change: Absolute position change
            market_impact: Market impact multiplier (1.0 = normal, >1.0 = high volatility)

        Returns:
            Total transaction cost
        """
        if position_change == 0:
            return 0.0

        # Base trading fee (maker vs taker based on urgency)
        urgency_factor = min(abs(position_change), 1.0)  # Higher urgency for larger trades
        trading_fee = self.maker_fee * (1 - urgency_factor) + self.taker_fee * urgency_factor

        # Market impact (slippage) - increases with trade size and volatility
        size_impact = min(abs(position_change) * 3, 0.0)  # Max 2% impact (increased)
        volatility_impact = self.slippage_impact * market_impact
        slippage = size_impact + volatility_impact

        # Latency cost (more significant in high-frequency scenarios)
        latency = self.latency_cost * urgency_factor

        # NEW: FREQUENCY-BASED PENALTY
        frequency_penalty = self._calculate_frequency_penalty()

        # NEW: HOLDING TIME PENALTY (penalty for changing position too soon)
        holding_time_penalty = self._calculate_holding_time_penalty()

        # total_cost = (trading_fee + slippage + latency + frequency_penalty + holding_time_penalty) * abs(position_change) * self.total_asset
        total_cost = (trading_fee + slippage + latency) * abs(position_change) * self.total_asset

        return total_cost

    def _calculate_frequency_penalty(self) -> float:
        """Calculate penalty based on recent trading frequency"""
        if len(self.trading_frequency_history) < 10:
            return 0.0

        # Calculate recent trading frequency (trades per period)
        recent_trades = sum(self.trading_frequency_history[-20:])  # Last 20 periods
        frequency_ratio = recent_trades / 20.0

        # Exponential penalty for high frequency trading
        if frequency_ratio > 0.3:  # More than 30% of periods with trades
            penalty = (frequency_ratio - 0.3) ** 2 * self.trading_frequency_penalty
            return penalty

        return 0.0

    def _calculate_holding_time_penalty(self) -> float:
        """Calculate penalty for changing position too soon"""
        if self.position_holding_time < self.min_holding_periods:
            # Exponential penalty for early position changes
            penalty_factor = (self.min_holding_periods - self.position_holding_time) / self.min_holding_periods
            penalty = penalty_factor ** 2 * self.holding_time_bonus
            return penalty

        return 0.0

    def get_reward(self) -> float:
        """
        Enhanced reward function for cryptocurrency trading

        Components:
        1. Base profit/loss
        2. Risk-adjusted returns (Sharpe ratio component)
        3. Drawdown penalty
        4. Transaction cost penalty
        5. Consistency bonus
        6. Volatility targeting
        """
        # Calculate position changes and transaction costs
        cur_pos = self.cur_action
        prev_pos = self.shares * self.cur_close / self.total_asset
        prev_shares = self.shares
        diff_pos = cur_pos - prev_pos

        # Enhanced transaction cost calculation
        current_volatility = self._estimate_current_volatility()
        market_impact = max(1.0, current_volatility / self.volatility_target)
        transaction_cost = self.calculate_enhanced_transaction_costs(abs(diff_pos), market_impact)

        # NEW: UPDATE HOLDING TIME TRACKING
        position_changed = abs(diff_pos) > self.min_action

        if position_changed:
            # Reset holding time when position changes
            self.position_holding_time = 0
            self.last_position_change_time = 0
            self.consecutive_holds = 0
            self.recent_trades_count += 1

            # Track trading frequency
            self.trading_frequency_history.append(1)
        else:
            # Increment holding time when position is maintained
            self.position_holding_time += 1
            self.last_position_change_time += 1
            self.consecutive_holds += 1

            # Track no trading
            self.trading_frequency_history.append(0)

        # Keep trading frequency history manageable
        if len(self.trading_frequency_history) > 100:
            self.trading_frequency_history = self.trading_frequency_history[-50:]

        # Execute position change with enhanced cost model
        if diff_pos > self.min_action:    # OPEN
            self.shares = cur_pos * self.total_asset / self.cur_close
            self.cash = (1 - cur_pos) * self.total_asset

            # adjust shares and cash
            if prev_pos >= 0:           # long, open long, cost by share
                self.shares -= transaction_cost / self.cur_close
            else:
                # short, close short, cost by cash
                self.cash -= transaction_cost

            self.pos_list.append(cur_pos)
            self.turnover_list.append(np.abs(diff_pos))
            self.episode_orders += 1
            self.prev_pos = cur_pos
            self.prev_state = self.cur_state
        elif diff_pos < -self.min_action:   # CLOSE
            self.shares = cur_pos * self.total_asset / self.cur_close
            self.cash = (1 - cur_pos) * self.total_asset
            if prev_pos <= 0:       # short, open short, cost by share
                self.shares += transaction_cost / self.cur_close
            else:
                # long, close long, cost by cash
                self.cash -= transaction_cost
            self.pos_list.append(cur_pos)
            self.turnover_list.append(np.abs(diff_pos))
            self.episode_orders += 1
            self.prev_pos = cur_pos
            self.prev_state = self.cur_state
        else:
            self.pos_list.append(prev_pos)
            self.turnover_list.append(0)

        self.next_total_asset = float(np.sum(self.next_close * self.shares) + self.cash)
        # Base reward: absolute profit/loss
        base_reward = self.next_total_asset - self.total_asset

        # Track returns for risk metrics
        if self.total_asset > 0:
            period_return = (self.next_total_asset - self.total_asset) / self.total_asset
            self.returns_history.append(period_return)

            # Keep only recent history for efficiency
            if len(self.returns_history) > 1000:
                self.returns_history = self.returns_history[-500:]

        # Update drawdown tracking
        if self.next_total_asset > self.peak_value:
            self.peak_value = self.next_total_asset
            self.current_drawdown = 0.0
        else:
            self.current_drawdown = (self.peak_value - self.next_total_asset) / self.peak_value

        self.drawdown_history.append(self.current_drawdown)

        # Risk-adjusted reward components
        reward_components = self._calculate_reward_components(base_reward, transaction_cost)

        # Combine all reward components including NEW holding time incentives and sparse reward optimization
        total_reward = (
            reward_components['base_reward'] +
            reward_components['sharpe_component'] * self.sharpe_weight +
            reward_components['drawdown_penalty'] * self.drawdown_penalty +
            reward_components['turnover_penalty'] * self.turnover_penalty +
            reward_components['consistency_bonus'] * self.consistency_bonus +
            reward_components['holding_time_bonus'] * self.holding_time_bonus +
            reward_components['position_momentum_bonus'] * self.position_momentum_bonus +
            reward_components['sparse_reward_bonus'] +  # NEW: Always include sparse reward optimization
            reward_components['exploration_bonus'] +    # NEW: Exploration bonus for long-only
            reward_components['potential_reward']       # NEW: Potential-based reward shaping
        )

        # Apply volatility targeting adjustment
        vol_adjustment = self._calculate_volatility_adjustment()
        total_reward *= vol_adjustment

        # Log detailed reward breakdown for evaluation
        if self.eval and np.random.rand() < self.eval_print_ratio:
            pos_ratio = f"{self.episode_positive_samples}/{self.episode_total_samples}" if hasattr(self, 'episode_positive_samples') else "N/A"
            logger.info(f'{self.cur_ts}, pos={cur_pos:.3f}, action={self.cur_action:.3f}, '
                       f'base_reward={reward_components["base_reward"]:.2f}, '
                       f'sharpe={reward_components["sharpe_component"]:.2f}, '
                       f'drawdown_penalty={reward_components["drawdown_penalty"]:.2f}, '
                       f'turnover_penalty={reward_components["turnover_penalty"]:.2f}, '
                       f'consistency_bonus={reward_components["consistency_bonus"]:.2f}, '
                       f'holding_time_bonus={reward_components["holding_time_bonus"]:.2f}, '
                       f'position_momentum_bonus={reward_components["position_momentum_bonus"]:.2f}, '
                       f'sparse_reward_bonus={reward_components["sparse_reward_bonus"]:.2f}, '
                       f'exploration_bonus={reward_components["exploration_bonus"]:.2f}, '
                       f'potential_reward={reward_components["potential_reward"]:.2f}, '
                       f'total_reward={total_reward:.2f}, '
                       f'total_asset={self.total_asset:.0f}, '
                       f'pos_samples={pos_ratio}')

        if cur_pos > 0:
            self.open_reward.append(base_reward)
        elif cur_pos < 0:
            self.close_reward.append(base_reward)
        else:
            self.hold_reward.append(base_reward)


        self.reward_list.append(total_reward)
        return total_reward

    def _estimate_current_volatility(self) -> float:
        """Estimate current market volatility"""
        if len(self.returns_history) < 10:
            return self.volatility_target

        recent_returns = self.returns_history[-20:]
        return np.std(recent_returns) * np.sqrt(525600)  # Annualized

    def _calculate_reward_components(self, base_reward: float, transaction_cost: float) -> Dict[str, float]:
        """Calculate individual reward components including NEW holding time incentives and sparse reward optimization"""
        components = {
            'base_reward': base_reward,
            'sharpe_component': 0.0,
            'drawdown_penalty': 0.0,
            'turnover_penalty': -transaction_cost,
            'consistency_bonus': 0.0,
            'holding_time_bonus': 0.0,  # NEW
            'position_momentum_bonus': 0.0,  # NEW
            'sparse_reward_bonus': 0.0,  # NEW: Bonus for sparse positive samples
            'exploration_bonus': 0.0,  # NEW: Exploration bonus for long-only
            'potential_reward': 0.0  # NEW: Potential-based reward shaping
        }

        # Sharpe ratio component (only if we have enough history)
        if len(self.returns_history) >= 120:
            returns_array = np.array(self.returns_history[-120:])
            if np.std(returns_array) > 0:
                sharpe_ratio = (np.mean(returns_array) - self.risk_free_rate / 525600) / np.std(returns_array)
                components['sharpe_component'] = sharpe_ratio * abs(base_reward)

        # Drawdown penalty
        if self.current_drawdown > 0:
            penalty_factor = min(self.current_drawdown / self.max_drawdown_threshold, 1.0)
            components['drawdown_penalty'] = -penalty_factor * abs(base_reward)

        # Consistency bonus (reward for steady performance)
        if len(self.returns_history) >= 10:
            recent_returns = np.array(self.returns_history[-10:])
            if np.std(recent_returns) > 0:
                consistency_score = 1.0 / (1.0 + np.std(recent_returns) * 100)
                components['consistency_bonus'] = consistency_score * abs(base_reward) * 0.1

        # NEW: HOLDING TIME BONUS (reward for maintaining positions)
        if self.position_holding_time >= self.min_holding_periods:
            # Exponential bonus for longer holding periods
            holding_bonus_factor = min(self.position_holding_time / (self.min_holding_periods * 2), 2.0)
            components['holding_time_bonus'] = holding_bonus_factor * abs(base_reward) * 0.2

        # NEW: POSITION MOMENTUM BONUS (reward for consistent position direction)
        if self.consecutive_holds >= 3:  # At least 3 consecutive holds
            momentum_factor = min(self.consecutive_holds / 10.0, 1.0)  # Max factor of 1.0
            components['position_momentum_bonus'] = momentum_factor * abs(base_reward) * 0.05

        # NEW: SPARSE REWARD OPTIMIZATION FOR LONG-ONLY POSITIONS
        components.update(self._calculate_sparse_reward_components(base_reward))

        return components

    def _calculate_sparse_reward_components(self, base_reward: float) -> Dict[str, float]:
        """
        Calculate additional reward components to handle sparse positive samples in long-only scenarios

        Args:
            base_reward: Base reward from the environment

        Returns:
            Dictionary with sparse reward optimization components
        """
        sparse_components = {
            'sparse_reward_bonus': 0.0,
            'exploration_bonus': 0.0,
            'potential_reward': 0.0
        }

        # Get current score and position
        current_score = self.raw_state[0] if hasattr(self, 'raw_state') and len(self.raw_state) > 0 else 0.0
        current_position = self.cur_action if hasattr(self, 'cur_action') else 0.0

        # 1. SPARSE REWARD BONUS: Extra reward for positive samples in sparse environments
        if hasattr(self, 'episode_positive_samples') and hasattr(self, 'episode_total_samples'):
            if self.episode_total_samples > 0:
                positive_ratio = self.episode_positive_samples / self.episode_total_samples

                # If positive samples are sparse (< 20%), provide bonus for positive actions
                if positive_ratio < 0.2:
                    score_threshold = getattr(self, 'score_thres', 0.4)

                    # Bonus for taking action on high-score samples
                    if current_score > score_threshold and current_position > self.min_action:
                        sparsity_factor = (0.2 - positive_ratio) / 0.2  # Higher bonus for sparser data
                        score_strength = (current_score - score_threshold) / (1.0 - score_threshold)
                        sparse_components['sparse_reward_bonus'] = sparsity_factor * score_strength * abs(base_reward) * 0.3

                    # Small bonus for holding when score is below threshold (avoiding false positives)
                    elif current_score <= score_threshold and current_position <= self.min_action:
                        sparse_components['sparse_reward_bonus'] = sparsity_factor * 0.1 * abs(base_reward)

        # 2. EXPLORATION BONUS: Encourage exploration in long-only scenarios
        if not getattr(self, 'enable_short', True):  # Long-only mode
            # Bonus for exploring different position sizes
            if hasattr(self, 'cur_action_list') and len(self.cur_action_list) > 10:
                recent_actions = self.cur_action_list[-10:]
                action_diversity = np.std(recent_actions) if len(recent_actions) > 1 else 0.0

                # Reward moderate diversity (not too conservative, not too erratic)
                optimal_diversity = 0.15  # Target standard deviation
                diversity_score = 1.0 - abs(action_diversity - optimal_diversity) / optimal_diversity
                diversity_score = max(0.0, diversity_score)

                sparse_components['exploration_bonus'] = diversity_score * abs(base_reward) * 0.1

        # 3. POTENTIAL-BASED REWARD SHAPING: Provide intermediate rewards
        if hasattr(self, 'cur_state') and self.cur_state is not None:
            # Calculate potential based on current state features
            potential = self._calculate_state_potential()

            # Potential-based shaping: F(s,a,s') = γ * Φ(s') - Φ(s)
            if hasattr(self, 'prev_potential'):
                gamma = getattr(self, 'gamma', 0.99)
                potential_diff = gamma * potential - self.prev_potential
                sparse_components['potential_reward'] = potential_diff * 0.05  # Small weight

            self.prev_potential = potential

        return sparse_components

    def _calculate_state_potential(self) -> float:
        """
        Calculate state potential for reward shaping

        Returns:
            Potential value for current state
        """
        if not hasattr(self, 'cur_state') or self.cur_state is None:
            return 0.0

        potential = 0.0

        # Score-based potential
        if len(self.cur_state) > 0:
            score = self.cur_state[0]
            score_threshold = getattr(self, 'score_thres', 0.4)

            # Higher potential for higher scores
            if score > score_threshold:
                potential += (score - score_threshold) * 2.0
            else:
                potential += (score - score_threshold) * 0.5  # Small negative potential

        # Volatility-based potential (prefer lower volatility for long positions)
        current_vol = self._estimate_current_volatility()
        vol_target = self.volatility_target
        if current_vol > 0:
            vol_ratio = vol_target / current_vol
            potential += np.clip(vol_ratio - 1.0, -0.5, 0.5)  # Prefer lower volatility

        # Trend-based potential (prefer upward trends for long positions)
        if len(self.cur_state) > 6:  # Assuming price change features are available
            # Use short-term price changes as trend indicator
            short_trend = self.cur_state[1] if len(self.cur_state) > 1 else 0.0  # pre_cr_1
            medium_trend = self.cur_state[3] if len(self.cur_state) > 3 else 0.0  # pre_cr_5

            # Positive trends increase potential
            trend_score = (short_trend * 0.6 + medium_trend * 0.4)
            potential += trend_score * 1.0

        return potential

    def _calculate_volatility_adjustment(self) -> float:
        """Calculate volatility targeting adjustment"""
        current_vol = self._estimate_current_volatility()
        if current_vol <= 0:
            return 1.0

        # Adjust reward based on volatility targeting
        vol_ratio = self.volatility_target / current_vol
        # Smooth adjustment to avoid extreme values
        return np.clip(vol_ratio, 0.5, 2.0)

    def _norm_action(self, action):
        """Enhanced action normalization with risk management"""
        if self.action_dim > 1:
            action = action / self.action_dim
        else:
            if self.enable_short:
                action = np.clip(action[0], -1, 1)
            else:
                action = np.clip(action[0], 0, 1)

        # Apply minimum position size threshold
        if abs(action) < self.min_position_size:
            action = 0.0

        # Apply volatility-based position scaling
        current_vol = self._estimate_current_volatility()
        if current_vol > self.volatility_target * 1.5:  # High volatility
            action *= 0.7  # Reduce position size
        elif current_vol < self.volatility_target * 0.5:  # Low volatility
            action *= 1.3  # Increase position size

        # Apply position size limits
        action = np.clip(action, -self.max_position_size, self.max_position_size)
        return action

    def get_state(self):
        """
        Enhanced state representation for cryptocurrency trading

        Returns comprehensive state including:
        - Original features (score, price changes)
        - Technical indicators (RSI, MACD, Bollinger Bands, etc.)
        - Volatility measures
        - Volume features
        - Market microstructure indicators
        - Risk management metrics
        """
        # Get base state from parent class
        base_state = super().get_state()

        # Extract OHLCV data for technical analysis
        try:
            # Get recent OHLCV data for technical indicator calculation
            lookback_period = max(100, max(self.ta_params.values()) + 10)
            start_idx = max(0, self.cur_kl - lookback_period)
            end_idx = self.cur_kl + self.last_kl_offset + 1

            if end_idx <= len(self.data):
                ohlcv_data = self.data.iloc[start_idx:end_idx][self.olhcv_cols].values

                # Calculate technical indicators
                if len(ohlcv_data) > 0:
                    indicators = self.calculate_technical_indicators(ohlcv_data)

                    # Create enhanced state vector
                    enhanced_features = []
                    for feature in self.feature_columns:
                        if feature == 'score':
                            # Keep original score from base state
                            enhanced_features.append(base_state[0])
                        elif feature.startswith('pre_cr_'):
                            # Keep original price change features
                            feature_idx = self.feature_columns.index(feature)
                            if feature_idx < len(base_state) - self.info_dim:
                                enhanced_features.append(base_state[feature_idx])
                            else:
                                enhanced_features.append(0.0)
                        else:
                            # Add new technical indicators
                            enhanced_features.append(indicators.get(feature, 0.0))

                    # Add risk management features
                    risk_features = self._get_risk_management_features()
                    enhanced_features.extend(risk_features)

                    # Add portfolio information (normalized capital amounts)
                    # portfolio_info = base_state[-self.info_dim:]
                    # enhanced_features.extend(portfolio_info)

                    enhanced_state = np.array(enhanced_features, dtype=np.float32)

                    # Ensure no NaN or infinite values
                    enhanced_state = np.nan_to_num(enhanced_state, nan=0.0, posinf=1.0, neginf=-1.0)

                    self.cur_state = enhanced_state
                    self.raw_state = enhanced_state
                    return enhanced_state

        except Exception as e:
            logger.warning(f"Error calculating enhanced state: {e}, falling back to base state")

        # Fallback to base state if enhancement fails
        return base_state

    def _get_risk_management_features(self) -> List[float]:
        """Get additional risk management features for state representation"""
        features = []

        # Current drawdown level
        features.append(self.current_drawdown)

        # Position concentration (how much of portfolio is in current position)
        current_position_value = self.shares * self.cur_close
        position_concentration = current_position_value / self.total_asset if self.total_asset > 0 else 0.0
        features.append(position_concentration)

        # Recent volatility estimate
        current_vol = self._estimate_current_volatility()
        vol_normalized = current_vol / self.volatility_target
        features.append(vol_normalized)

        # Recent performance (last 10 periods)
        if len(self.returns_history) >= 10:
            recent_performance = np.mean(self.returns_history[-10:])
            features.append(recent_performance * 100)  # Scale for better numerical properties
        else:
            features.append(0.0)

        # Trading frequency (orders per period)
        if hasattr(self, 'episode_orders') and self.cur_kl > 0:
            trading_frequency = self.episode_orders / self.cur_kl
            features.append(trading_frequency)
        else:
            features.append(0.0)

        # NEW: HOLDING TIME FEATURES
        # Normalized position holding time
        normalized_holding_time = min(self.position_holding_time / (self.min_holding_periods * 2), 1.0)
        features.append(normalized_holding_time)

        # Time since last position change
        normalized_time_since_change = min(self.last_position_change_time / 20.0, 1.0)
        features.append(normalized_time_since_change)

        # Position stability score (based on consecutive holds)
        stability_score = min(self.consecutive_holds / 10.0, 1.0)
        features.append(stability_score)

        # Recent trading frequency (last 20 periods)
        if len(self.trading_frequency_history) >= 10:
            recent_trading_freq = np.mean(self.trading_frequency_history[-20:])
            features.append(recent_trading_freq)
        else:
            features.append(0.0)

        # Market regime stability indicator
        regime_stability = self._calculate_regime_stability()
        features.append(regime_stability)

        return features

    def _calculate_regime_stability(self) -> float:
        """Calculate market regime stability indicator"""
        if len(self.returns_history) < 20:
            return 0.5  # Neutral stability

        # Calculate rolling volatility to assess regime stability
        recent_returns = np.array(self.returns_history[-20:])
        rolling_vol = []

        for i in range(5, len(recent_returns)):
            window_vol = np.std(recent_returns[i-5:i])
            rolling_vol.append(window_vol)

        if len(rolling_vol) < 2:
            return 0.5

        # Stability is inversely related to volatility of volatility
        vol_of_vol = np.std(rolling_vol)
        stability = 1.0 / (1.0 + vol_of_vol * 1000)  # Scale for numerical stability

        return np.clip(stability, 0.0, 1.0)

    def _calculate_symbol_positive_ratio(self, symbol_data: pd.DataFrame, score_threshold: float) -> float:
        """Calculate the ratio of positive samples for a given symbol"""
        if len(symbol_data) == 0:
            return 0.0

        positive_count = np.sum(symbol_data['score'] > score_threshold)
        return positive_count / len(symbol_data)

    def _adaptive_symbol_selection(self) -> str:
        """
        Intelligently select symbols based on positive sample density and performance history

        Returns:
            Selected symbol for the episode
        """
        if not self.enable_adaptive_sampling:
            # Fall back to random selection
            return np.random.choice(self.symbols, 1)[0]

        symbol_scores = {}

        for symbol in self.symbols:
            # Calculate positive sample ratio
            if symbol not in self.symbol_positive_ratios:
                # Initialize with data analysis
                try:
                    data = super()._load_data(symbol)
                    score_threshold = getattr(self, 'score_thres_dict', {}).get(symbol, 0.4)
                    self.symbol_positive_ratios[symbol] = self._calculate_symbol_positive_ratio(data, score_threshold)
                except:
                    self.symbol_positive_ratios[symbol] = 0.1  # Default low ratio

            positive_ratio = self.symbol_positive_ratios[symbol]

            # Calculate performance history score
            performance_score = self.symbol_performance_history.get(symbol, 0.0)

            # Combined score: balance positive ratio and historical performance
            # Higher weight for positive ratio in long-only scenarios
            combined_score = (positive_ratio * 0.7 + performance_score * 0.3)

            # Add exploration bonus for less-sampled symbols
            exploration_bonus = 1.0 / (1.0 + len(self.symbol_performance_history.get(symbol, [])))
            combined_score += exploration_bonus * 0.1

            symbol_scores[symbol] = combined_score

        # Weighted random selection based on scores
        symbols = list(symbol_scores.keys())
        scores = list(symbol_scores.values())

        # Normalize scores to probabilities
        scores = np.array(scores)
        if np.sum(scores) > 0:
            probabilities = scores / np.sum(scores)
        else:
            probabilities = np.ones(len(symbols)) / len(symbols)

        selected_symbol = np.random.choice(symbols, p=probabilities)
        return selected_symbol

    def _adaptive_episode_filtering(self, symbol_data: pd.DataFrame, symbol: str) -> bool:
        """
        Adaptive filtering for episode selection based on positive sample density

        Args:
            symbol_data: Data for the selected symbol
            symbol: Symbol name

        Returns:
            True if episode should be used, False otherwise
        """
        if not self.enable_adaptive_sampling:
            # Fall back to original filtering
            score_threshold = getattr(self, 'score_thres_dict', {}).get(symbol, 0.4)
            score_count = np.sum(symbol_data['score'] > score_threshold)
            return len(symbol_data) > 0 and score_count > 10

        # Adaptive threshold based on symbol characteristics
        base_threshold = getattr(self, 'score_thres_dict', {}).get(symbol, 0.4)
        adaptive_threshold = base_threshold * self.adaptive_threshold_factor

        positive_count = np.sum(symbol_data['score'] > adaptive_threshold)
        total_samples = len(symbol_data)

        if total_samples == 0:
            return False

        positive_ratio = positive_count / total_samples

        # More lenient filtering for long-only scenarios
        # Accept episodes with at least min_positive_samples and reasonable ratio
        min_samples_met = positive_count >= self.min_positive_samples
        ratio_acceptable = positive_ratio >= (self.sample_balance_ratio * 0.5)  # 50% of target ratio

        return min_samples_met and ratio_acceptable

    def _augment_positive_samples(self, symbol_data: pd.DataFrame) -> pd.DataFrame:
        """
        Augment positive samples to improve balance for long-only training

        Args:
            symbol_data: Original symbol data

        Returns:
            Augmented data with enhanced positive samples
        """
        if not self.enable_adaptive_sampling:
            return symbol_data

        # Identify positive samples
        score_threshold = getattr(self, 'score_thres_dict', {}).get(self.cur_symbol, 0.4)
        positive_mask = symbol_data['score'] > score_threshold
        positive_samples = symbol_data[positive_mask].copy()

        if len(positive_samples) == 0:
            return symbol_data

        # Calculate current positive ratio
        current_ratio = len(positive_samples) / len(symbol_data)
        target_ratio = self.sample_balance_ratio

        # If we need more positive samples
        if current_ratio < target_ratio:
            # Calculate how many additional samples we need
            total_samples = len(symbol_data)
            needed_positive = int(target_ratio * total_samples) - len(positive_samples)

            if needed_positive > 0:
                # Create augmented positive samples through slight perturbations
                augmented_samples = []

                for _ in range(min(needed_positive, len(positive_samples))):
                    # Select a random positive sample
                    base_sample = positive_samples.sample(1).iloc[0].copy()

                    # Add small noise to numerical features (except score)
                    for col in ['pre_cr_1', 'pre_cr_3', 'pre_cr_5', 'pre_cr_15', 'pre_cr_30', 'pre_cr_60']:
                        if col in base_sample:
                            noise_factor = 0.02  # 2% noise
                            noise = np.random.normal(0, abs(base_sample[col]) * noise_factor)
                            base_sample[col] += noise

                    # Slightly boost the score to maintain positive classification
                    if 'score' in base_sample:
                        score_boost = np.random.uniform(0.01, 0.03)  # Small boost
                        base_sample['score'] += score_boost

                    augmented_samples.append(base_sample)

                if augmented_samples:
                    augmented_df = pd.DataFrame(augmented_samples)
                    symbol_data = pd.concat([symbol_data, augmented_df], ignore_index=True)

                    # Shuffle to distribute augmented samples
                    symbol_data = symbol_data.sample(frac=1).reset_index(drop=True)

        return symbol_data


"""
CTAEnvV5 Optimization Summary for Cryptocurrency Trading
========================================================

This optimized environment (CTAEnvV5) enhances the original CTAEnvV3 with specific improvements
for minute-level cryptocurrency trading:

## Key Optimizations:

### 1. Enhanced State Representation (43 features vs 7 original):
   - **Price Features**: Original score + multi-timeframe price changes
   - **Volatility Features**: 1m, 5m, 15m, 60m volatility + realized vol + vol ratios
   - **Technical Indicators**: RSI(14,30), MACD, Bollinger Bands, EMAs, SMA, Momentum
   - **Volume Features**: Volume ratios, VWAP, volume volatility
   - **Market Microstructure**: Bid-ask spread, order imbalance, trade intensity
   - **Cross-timeframe**: Trend alignment, support/resistance, breakout signals
   - **Risk Management**: Drawdown, position concentration, recent performance

### 2. Sophisticated Reward Function:
   - **Base Reward**: Absolute profit/loss (as original)
   - **Risk-Adjusted Component**: Sharpe ratio weighting (30% weight)
   - **Drawdown Penalty**: Penalizes excessive drawdowns (2x weight)
   - **Transaction Cost Penalty**: Realistic cost modeling (0.1x weight)
   - **Consistency Bonus**: Rewards steady performance (0.2x weight)
   - **Volatility Targeting**: Adjusts rewards based on vol target (20% annual)

### 3. Advanced Transaction Cost Modeling:
   - **Maker/Taker Fees**: 0.1%/0.15% (typical crypto exchange rates)
   - **Market Impact**: Size-dependent slippage (up to 1% for large trades)
   - **Volatility Impact**: Higher costs during volatile periods
   - **Latency Costs**: Accounts for execution delays

### 4. Enhanced Risk Management:
   - **Position Limits**: Max 95%, min 1% position sizes
   - **Volatility-Based Sizing**: Reduces positions during high volatility
   - **Drawdown Monitoring**: 15% maximum drawdown threshold
   - **Dynamic Position Decay**: Gradual position reduction mechanism

### 5. Crypto-Specific Features:
   - **High-Frequency Indicators**: Suitable for minute-level trading
   - **Volatility Regime Detection**: Adapts to crypto market conditions
   - **Multi-timeframe Analysis**: Captures crypto trend persistence
   - **Volume Analysis**: Important for crypto liquidity assessment

## Usage Example:
```python
env = CTAEnvV5(
    symbols=['BTCUSDT', 'ETHUSDT'],
    initial_asset=100000,
    transaction_cost=0.001,  # Will be overridden by maker/taker fees
    volatility_target=0.20,
    max_drawdown_threshold=0.15,
    enable_short=True,
    reward_type='absolute'
)
```

## Performance Expectations:
- **Better Risk-Adjusted Returns**: Sharpe ratio optimization
- **Lower Drawdowns**: Active drawdown management
- **More Realistic Costs**: Accurate transaction cost modeling
- **Improved Stability**: Volatility targeting and risk management
- **Enhanced Signal Quality**: Comprehensive technical analysis

## Compatibility:
- Fully compatible with ElegantRL framework
- Maintains CTAEnvV3 interface
- Can be used as drop-in replacement
- Supports both long-only and long/short strategies
"""