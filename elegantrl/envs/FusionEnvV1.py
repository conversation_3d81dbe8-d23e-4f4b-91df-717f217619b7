import numpy as np
import pandas as pd
import os
from loguru import logger
from functorch import vmap
from copy import deepcopy
from sklearn.preprocessing import StandardScaler
from elegantrl.utils import RewardScaling
from elegantrl.envs.CTAEnvV3 import CTAEnvV3

# CTA V2: 0429
# 简化输入，仅输入模型分
score_thres_dict = {'DOGE#USDT:USDT': 0.47, 'XRP#USDT:USDT': 0.41, 'SOL#USDT:USDT': 0.47, 'ADA#USDT:USDT': 0.41, 'LINK#USDT:USDT': 0.47,
                    'LTC#USDT:USDT': 0.43, 'AVAX#USDT:USDT': 0.45, 'UNI#USDT:USDT': 0.50, 'DOT#USDT:USDT': 0.45, 'INJ#USDT:USDT': 0.46}

class FusionEnvV1(CTAEnvV3):

    env_name = 'FusionEnv-V1'
    reward_type = 'sharpe'
    olhcv_cols = ['open', 'close', 'low', 'high', 'vol', 'val']
    cwd = ['data_snap/pred_model_5min_gain_v5_432882_online',
           'data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v3',
           'data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v9']
    ohlcv_dir = f'data_snap/portfolio_v1'
    feature_columns = [
        f'score_{i}' for i in range(len(cwd))
    ] + ['pre_cr_1', 'pre_cr_3', 'pre_cr_5', 'pre_cr_15', 'pre_cr_30', 'pre_cr_60']
    score_thres = 0.4
    score_base = 0.1
    pos_frac = 5

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


    def _check_symbol_exist_in_all_cwd(self, symbol):
        for cwd in self.cwd:
            if not os.path.exists(f'{cwd}/{symbol}'):
                logger.error(f'{symbol} not exist in {cwd}')
                return False
        return True

    def _load_symbols(self, symbols:list[str]):
        if symbols == []:
            symbols = ['DOGE#USDT:USDT']
        filtered_symbols = []

        for cwd in self.cwd:
            if not os.path.exists(cwd):
                logger.error(f'{cwd} not exist')
                exit(-1)

        for sym in symbols:
            if self._check_symbol_exist_in_all_cwd(sym):
                filtered_symbols.append(sym)
        logger.info(f'load {filtered_symbols=}')

        return filtered_symbols
    
    def _load_data_from_cwd(self, symbol, cwd):
        v = pd.read_parquet(f'{cwd}/{symbol}')
        v.drop(columns=['extra', 'action', 'createtime'], inplace=True)
        v = v.drop_duplicates(subset=['datatime', 'symbol'])
        v = v.replace([np.inf, -np.inf], np.nan)
        v.set_index('datatime', inplace=True)
        v.sort_index(inplace=True)
        return v

    def _load_data(self, symbol):
        v = None
        score_num = 0
        for cwd in self.cwd:
            if v is None:
                v = self._load_data_from_cwd(symbol, cwd)
                v.rename(columns={'score': f'score_{score_num}'}, inplace=True)
                score_num += 1
            else:
                v_ = self._load_data_from_cwd(symbol, cwd)
                v_.rename(columns={'score': f'score_{score_num}'}, inplace=True)
                score_num += 1
                v = v.merge(v_, on=['datatime', 'symbol', 'exchange'], how='inner')

        if self.feature_norm:
            transform_fit_data = v[self.transform_st:self.transform_et].copy()
            transform_df = self._merge_olhcv(symbol, transform_fit_data)
            if len(transform_df) == 0:
                logger.error(f'{symbol} has no data in {self.transform_st} to {self.transform_et}')
                return pd.DataFrame()
            transform_df = self._transform_features(transform_df)
            del transform_fit_data, transform_df

        v = v[self.start_date:self.end_date].copy()
        df = self._merge_olhcv(symbol, v)
        for col in self.feature_columns:
            if col in df.columns:
                df[col] = df[col].ffill()
            if 'pre_cr' in col:
                df[col] = np.log(df[col] + 1)
        if len(df) == 0:
            logger.error(f'{symbol}')
        if self.feature_norm:
            df = self._transform_features(df)
        return df.reset_index()

    def _norm_action(self, action):
        """
        标准化动作，确保动作在合理范围内
        """
        if self.action_dim > 1:
            action = int(action)
        else:
            if self.enable_short:
                # 支持做空：动作范围 [-1, 1]
                action = np.clip(action[0], -1, 1)
            else:
                # 仅做多：动作范围 [0, 1]
                # action = np.clip((action[0] + 1) / 2, 0, 1)
                action = np.clip(action[0], 0, 1)
            action = float(action)

        # 确保动作是有限数值
        if not np.isfinite(action):
            logger.warning(f"Invalid action detected: {action}, setting to 0")
            action = 0.0

        return action
    

    def step(self, action):
        """
        执行一步交易，修复时间对齐问题
        """
        self.cur_kl += 1
        cur_kl = self.cur_kl + self.last_kl_offset

        # 修复时间对齐：确保时间戳和价格的一致性
        # 当前时间戳对应当前价格，下一期时间戳对应下一期价格
        self.cur_ts = self.data.iloc[cur_kl-1]['datatime']  # 当前决策时间点
        self.cur_close = self.data.iloc[cur_kl-1]['close']   # 当前价格（决策时已知）
        self.next_close = self.data.iloc[cur_kl]['close']    # 下一期价格（奖励计算用）

        # 标准化动作
        self.cur_action = self._norm_action(action)
        self.cur_action_list.append(self.cur_action)

        # 计算奖励
        reward = self.get_reward()

        # 获取下一个状态
        state = self.get_state()

        # 更新资产和统计信息
        self.cumulative_returns = self.next_total_asset / self.initial_asset * 100
        self.total_asset = self.next_total_asset
        self.gamma_return = float(self.gamma_return * self.gamma + reward)
        self.prev_action = self.cur_action

        # 检查是否结束
        done = (self.cur_kl >= self.max_step) or (
            float(self.total_asset) <= float(self.initial_asset * self.capital_thresh))

        if done:
            # 计算最终统计信息
            hold_ret = float(self.next_close / self.init_close)

            # 计算基准收益
            if self.base_return_list:
                base_ret = 100 * (np.sum(self.base_return_list) / self.initial_asset + 1)
            else:
                base_ret = 100

            # 最终奖励调整
            final_reward_adjustment = 1 / (1 - self.gamma) * np.mean(self.reward_list) if self.reward_list else 0
            reward += final_reward_adjustment

            # 构建日志信息
            other_logging_str = self._build_logging_string(base_ret)

            # 记录最终结果
            log_msg = (f'{self.cur_kl}/{self.max_step}@{self.cur_ts}, {self.cur_symbol}, '
                      f'{int(self.total_asset)}/{int(self.initial_asset)}, '
                      f'cash={int(self.cash)}, share_value={self.shares*self.cur_close:.2f}, '
                      f'orders={self.episode_orders}, cum_ret={self.cumulative_returns:.2f}, '
                      f'hold_ret={hold_ret:.2f}, raw_rewards={np.sum(self.reward_list):.2f}, '
                      f'final_reward={reward:.2f}{other_logging_str}')

            if self.eval:
                logger.warning(log_msg)
            else:
                logger.info(log_msg)

            self.gamma_return = 0.0

        truncated = False
        return state, reward, done, truncated, {}
