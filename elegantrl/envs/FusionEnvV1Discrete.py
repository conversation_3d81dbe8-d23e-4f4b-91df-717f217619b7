import numpy as np
import pandas as pd
import os
from loguru import logger
from functorch import vmap
from copy import deepcopy
from sklearn.preprocessing import StandardScaler
from elegantrl.utils import RewardScaling
from elegantrl.envs.FusionEnvV1 import FusionEnvV1

# CTA V2: 0429
# 简化输入，仅输入模型分

open_thres_list = [0.45, 0.78, 0.96]

class FusionEnvV1Discrete(FusionEnvV1):

    env_name = 'FusionEnv-V1-Discrete'

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def get_reward(self):
        """
        计算奖励函数，修复手续费计算和时间对齐问题
        """
        score = self.raw_state[self.cur_action]
        sell_close = self.sell_close
        possible_profit_with_cost = sell_close * (1-self.transaction_cost)**2 - self.cur_close
        possible_profit_without_cost = sell_close * (1-self.transaction_cost) - self.cur_close
        base_return = 0
        # base by score
        if self.raw_state[0] > open_thres_list[0]:
            # raw_open_cap = max_open_capital * max(self.min_action, (score - self.score_base))
            raw_open_cap = self.max_open_capital * (score - self.score_base)
            # raw_open_cap = min(self.cash, self.max_open_capital) * (score - self.score_base)
            buy_volume = raw_open_cap / self.cur_close
            # wait_to_buy_volume = self.clean_position(buy_volume, dry_run=True)
            wait_to_buy_volume = buy_volume
            base_return = possible_profit_with_cost * wait_to_buy_volume + \
                    possible_profit_without_cost * (buy_volume - wait_to_buy_volume)
        self.base_return_list.append(base_return)
        reward = 0
        # OPEN
        if score >= open_thres_list[self.cur_action]:
            self.stat_info['open'] += 1
            open_cap = self.max_open_capital * (score - self.score_base)
            buy_volume = open_cap / self.cur_close
            wait_to_buy_volume = self.clean_position(buy_volume)
            if wait_to_buy_volume > 0:
                if wait_to_buy_volume * self.cur_close > self.cash:
                    buy_volume -= wait_to_buy_volume
                    wait_to_buy_volume = self.cash / self.cur_close
                    buy_volume += wait_to_buy_volume
                self.cash -= wait_to_buy_volume * self.cur_close
                self.episode_orders += 1
                self.shares += wait_to_buy_volume * (1 - self.transaction_cost)
            if buy_volume > 0:
                real_buy_volume = buy_volume - wait_to_buy_volume * self.transaction_cost
                self.positon.append((self.cur_ts, real_buy_volume))

            reward += possible_profit_with_cost * wait_to_buy_volume + (buy_volume - wait_to_buy_volume) * possible_profit_without_cost
        # HOLD
        else:
            self.stat_info['hold'] += 1
            # reward -= encourage_penalty * self.total_asset
            _ = self.clean_position()
        
        # 计算下一期总资产（考虑价格变化）
        self.next_total_asset = float(self.shares * self.next_close + self.cash)

        # 基础奖励：资产变化
        asset_change = self.next_total_asset - self.total_asset

        if self.reward_type == 'asset':
            reward = asset_change
            # # 计算持有策略的收益作为基准
            # if self.total_asset > 0:
            #     hold_return = prev_shares * (self.next_close - self.cur_close)
            #     reward -= hold_return
        elif self.reward_type == 'sharpe':
            reward = 0
            # Track returns for risk metrics
            period_return = asset_change / self.total_asset
            self.returns_history.append(period_return)
            if len(self.returns_history) >= self.sharpe_windows:
                returns_array = np.array(self.returns_history[-self.sharpe_windows:])
                if np.std(returns_array) > 0:
                    sharpe_ratio = (np.mean(returns_array) - self.risk_free_rate / 525600) / np.std(returns_array)
                    reward = sharpe_ratio

        # 风险调整：对过度交易进行惩罚
        # if transaction_cost > 0:
        #     reward -= transaction_cost  # 扣除交易成本

        # 记录不同类型的奖励
        if score >= open_thres_list[self.cur_action]:
            self.open_reward.append(reward)
        else:
            self.hold_reward.append(reward)

        # 调试信息
        if self.eval and np.random.rand() < self.eval_print_ratio:
            logger.info(f'{self.cur_ts}, state={self.raw_state[:3]}, '
                       f'cash={self.cash:.2f}, shares={self.shares:.4f}, '
                       f'reward={reward:.4f}, total_asset={self.total_asset:.2f}')

        self.reward_list.append(reward)
        return reward

