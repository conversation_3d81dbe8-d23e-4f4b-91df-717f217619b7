#!/usr/bin/env python3
"""
Example: Optimized Cryptocurrency Trading with Longer Holding Times
===================================================================

This example demonstrates how to use the optimized CTAEnvV5 and AgentSACOpt
for cryptocurrency trading with longer position holding times.

Key Features:
- 15% minimum action threshold (3x increase)
- 30x higher turnover penalty
- 10x higher transaction costs
- Holding time bonuses and momentum rewards
- Enhanced state representation (49 features)
- Market regime-aware exploration
"""

import numpy as np
import torch as th
from typing import Dict, List
import matplotlib.pyplot as plt

# Import optimized components
from elegantrl.envs.CTAEnvV5 import CTAEnvV5
from elegantrl.agents.AgentSACOpt import AgentSACOpt
from elegantrl.train import Config
from OPTIMAL_PARAMETERS_CONFIG import get_optimal_env_config, get_optimal_agent_config


class OptimizedCryptoTrader:
    """
    Optimized cryptocurrency trader with longer holding time focus
    """
    
    def __init__(self, symbols: List[str] = None):
        """
        Initialize the optimized crypto trader
        
        Args:
            symbols: List of cryptocurrency symbols to trade
        """
        self.symbols = symbols or ['BTCUSDT', 'ETHUSDT']
        self.setup_environment()
        self.setup_agent()
        self.metrics = {
            'holding_times': [],
            'trading_frequency': [],
            'returns': [],
            'drawdowns': [],
            'transaction_costs': []
        }
    
    def setup_environment(self):
        """Setup optimized trading environment"""
        print("🏗️  Setting up optimized trading environment...")
        
        # Get optimal configuration
        env_config = get_optimal_env_config()
        env_config['symbols'] = self.symbols
        
        # Create optimized environment
        self.env = CTAEnvV5(**env_config)
        
        print(f"✅ Environment created with {self.env.state_dim} state features")
        print(f"   - min_action: {self.env.min_action} (15% threshold)")
        print(f"   - turnover_penalty: {self.env.turnover_penalty} (30x increase)")
        print(f"   - holding_time_bonus: {self.env.holding_time_bonus}")
        print(f"   - min_holding_periods: {self.env.min_holding_periods} minutes")
    
    def setup_agent(self):
        """Setup optimized SAC agent"""
        print("\n🤖 Setting up optimized SAC agent...")
        
        # Get optimal configuration
        args = get_optimal_agent_config()
        
        # Create optimized agent
        self.agent = AgentSACOpt(
            net_dims=args.net_dims,
            state_dim=self.env.state_dim,
            action_dim=self.env.action_dim,
            gpu_id=0,
            args=args
        )
        
        print(f"✅ Agent created with {args.net_dims} network architecture")
        print(f"   - learning_rate: {args.learning_rate}")
        print(f"   - target_entropy: {args.target_entropy} (conservative)")
        print(f"   - market_regime_window: {args.market_regime_window}")
    
    def run_episode(self, max_steps: int = 1000) -> Dict:
        """
        Run a single trading episode with holding time tracking
        
        Args:
            max_steps: Maximum steps per episode
            
        Returns:
            Episode metrics dictionary
        """
        state = self.env.reset()
        episode_metrics = {
            'total_return': 0.0,
            'total_trades': 0,
            'holding_times': [],
            'positions': [],
            'rewards': [],
            'transaction_costs': 0.0
        }
        
        current_holding_time = 0
        last_position = 0.0
        
        for step in range(max_steps):
            # Get action from agent
            state_tensor = th.tensor(state, dtype=th.float32).unsqueeze(0)
            action = self.agent.explore_action(state_tensor)
            action_np = action.detach().cpu().numpy().flatten()
            
            # Execute action
            next_state, reward, done, truncated, info = self.env.step(action_np)
            
            # Track holding time
            current_position = action_np[0]
            if abs(current_position - last_position) > self.env.min_action:
                # Position changed - record holding time
                if current_holding_time > 0:
                    episode_metrics['holding_times'].append(current_holding_time)
                current_holding_time = 0
                episode_metrics['total_trades'] += 1
            else:
                # Position held
                current_holding_time += 1
            
            # Update metrics
            episode_metrics['total_return'] += reward
            episode_metrics['positions'].append(current_position)
            episode_metrics['rewards'].append(reward)
            episode_metrics['transaction_costs'] += info.get('transaction_cost', 0)
            
            # Update for next step
            state = next_state
            last_position = current_position
            
            if done or truncated:
                break
        
        # Final holding time
        if current_holding_time > 0:
            episode_metrics['holding_times'].append(current_holding_time)
        
        return episode_metrics
    
    def evaluate_performance(self, num_episodes: int = 10) -> Dict:
        """
        Evaluate trading performance over multiple episodes
        
        Args:
            num_episodes: Number of episodes to evaluate
            
        Returns:
            Performance metrics dictionary
        """
        print(f"\n📊 Evaluating performance over {num_episodes} episodes...")
        
        all_metrics = []
        
        for episode in range(num_episodes):
            episode_metrics = self.run_episode()
            all_metrics.append(episode_metrics)
            
            if episode % 5 == 0:
                avg_holding = np.mean(episode_metrics['holding_times']) if episode_metrics['holding_times'] else 0
                print(f"   Episode {episode}: avg_holding={avg_holding:.1f}min, trades={episode_metrics['total_trades']}")
        
        # Aggregate metrics
        performance = {
            'average_holding_time': np.mean([
                np.mean(ep['holding_times']) if ep['holding_times'] else 0 
                for ep in all_metrics
            ]),
            'average_trades_per_episode': np.mean([ep['total_trades'] for ep in all_metrics]),
            'average_return': np.mean([ep['total_return'] for ep in all_metrics]),
            'trading_frequency': np.mean([
                ep['total_trades'] / len(ep['positions']) if ep['positions'] else 0
                for ep in all_metrics
            ]),
            'total_transaction_costs': np.sum([ep['transaction_costs'] for ep in all_metrics])
        }
        
        return performance, all_metrics
    
    def print_performance_summary(self, performance: Dict):
        """Print performance summary"""
        print("\n" + "="*60)
        print("PERFORMANCE SUMMARY")
        print("="*60)
        
        print(f"📈 Average Holding Time: {performance['average_holding_time']:.1f} minutes")
        print(f"🔄 Average Trades per Episode: {performance['average_trades_per_episode']:.1f}")
        print(f"📊 Trading Frequency: {performance['trading_frequency']:.1%}")
        print(f"💰 Average Return: {performance['average_return']:.2f}")
        print(f"💸 Total Transaction Costs: {performance['total_transaction_costs']:.2f}")
        
        # Performance assessment
        print("\n🎯 OPTIMIZATION ASSESSMENT:")
        
        if performance['average_holding_time'] >= 5.0:
            print("✅ Holding time target achieved (≥5 minutes)")
        else:
            print("⚠️  Holding time below target (<5 minutes)")
        
        if performance['trading_frequency'] <= 0.3:
            print("✅ Trading frequency target achieved (≤30%)")
        else:
            print("⚠️  Trading frequency above target (>30%)")
        
        if performance['average_return'] > 0:
            print("✅ Positive average returns achieved")
        else:
            print("⚠️  Negative average returns")
    
    def compare_with_baseline(self):
        """Compare optimized version with baseline parameters"""
        print("\n🔍 COMPARISON WITH BASELINE:")
        print("="*40)
        
        baseline_params = {
            'min_action': 0.05,
            'turnover_penalty': 0.1,
            'maker_fee': 0.0002,
            'holding_time_bonus': 0.0
        }
        
        optimized_params = {
            'min_action': self.env.min_action,
            'turnover_penalty': self.env.turnover_penalty,
            'maker_fee': self.env.maker_fee,
            'holding_time_bonus': self.env.holding_time_bonus
        }
        
        for param, baseline_val in baseline_params.items():
            optimized_val = optimized_params[param]
            if baseline_val > 0:
                ratio = optimized_val / baseline_val
                print(f"{param}: {baseline_val} → {optimized_val} ({ratio:.1f}x)")
            else:
                print(f"{param}: {baseline_val} → {optimized_val} (NEW)")


def main():
    """Main execution function"""
    print("Optimized Cryptocurrency Trading with Longer Holding Times")
    print("="*65)
    
    # Initialize trader
    trader = OptimizedCryptoTrader(symbols=['BTCUSDT'])
    
    # Compare with baseline
    trader.compare_with_baseline()
    
    # Evaluate performance
    performance, all_metrics = trader.evaluate_performance(num_episodes=5)
    
    # Print summary
    trader.print_performance_summary(performance)
    
    print("\n🚀 NEXT STEPS:")
    print("1. Run full backtesting with historical data")
    print("2. Monitor holding time vs profitability trade-offs")
    print("3. Adjust min_holding_periods based on market conditions")
    print("4. Deploy with real-time market data")
    
    return performance


if __name__ == "__main__":
    try:
        performance = main()
        print(f"\n✅ Example completed successfully!")
        print(f"Average holding time: {performance['average_holding_time']:.1f} minutes")
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        import traceback
        traceback.print_exc()
