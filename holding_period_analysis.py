#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓时间分析工具
优化交易分析代码，新增统计每次开仓到平仓之间的平均持仓时间功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

def calculate_holding_periods(res_data, position_threshold=0.005):
    """
    计算每次开仓到平仓之间的持仓时间
    从第一次开仓开始计算，到仓位归零算一次开平仓
    
    Args:
        res_data: 包含时间、仓位等信息的DataFrame
        position_threshold: 仓位阈值，低于此值认为是空仓
    
    Returns:
        list: 包含每个持仓周期详细信息的列表
    """
    holding_periods = []
    current_position_start = None
    
    for i, row in res_data.iterrows():
        current_pos = row['pos'] if 'pos' in row else 0
        current_time = row['time']
        
        # 如果当前没有持仓记录且仓位大于阈值，记录开仓时间
        if current_position_start is None and current_pos > position_threshold:
            current_position_start = current_time
            print(f"开仓检测: {current_time}, 仓位: {current_pos:.4f}")
        
        # 如果已有持仓记录且仓位归零（或接近零），计算持仓时间
        elif current_position_start is not None and current_pos <= position_threshold:
            holding_end = current_time
            holding_duration = holding_end - current_position_start
            
            period_info = {
                'start_time': current_position_start,
                'end_time': holding_end,
                'duration_minutes': holding_duration.total_seconds() / 60,
                'duration_hours': holding_duration.total_seconds() / 3600,
                'duration_days': holding_duration.total_seconds() / (3600 * 24)
            }
            
            holding_periods.append(period_info)
            print(f"平仓检测: {holding_end}, 持仓时长: {period_info['duration_minutes']:.1f}分钟")
            current_position_start = None
    
    # 如果最后还有未平仓的持仓，以最后一个时间点作为结束
    if current_position_start is not None:
        holding_end = res_data.iloc[-1]['time']
        holding_duration = holding_end - current_position_start
        
        period_info = {
            'start_time': current_position_start,
            'end_time': holding_end,
            'duration_minutes': holding_duration.total_seconds() / 60,
            'duration_hours': holding_duration.total_seconds() / 3600,
            'duration_days': holding_duration.total_seconds() / (3600 * 24),
            'is_incomplete': True  # 标记为未完成的持仓
        }
        
        holding_periods.append(period_info)
        print(f"未完成持仓: 开始于{current_position_start}, 当前持续{period_info['duration_minutes']:.1f}分钟")
    
    return holding_periods

def calculate_holding_statistics(holding_periods):
    """
    计算持仓时间的统计指标
    
    Args:
        holding_periods: 持仓周期列表
    
    Returns:
        dict: 包含各种统计指标的字典
    """
    if not holding_periods:
        return {
            'total_positions': 0,
            'avg_holding_minutes': 0,
            'avg_holding_hours': 0,
            'avg_holding_days': 0,
            'min_holding_minutes': 0,
            'max_holding_minutes': 0,
            'min_holding_hours': 0,
            'max_holding_hours': 0,
            'median_holding_minutes': 0,
            'std_holding_minutes': 0
        }
    
    durations_minutes = [period['duration_minutes'] for period in holding_periods]
    
    stats = {
        'total_positions': len(holding_periods),
        'avg_holding_minutes': np.mean(durations_minutes),
        'avg_holding_hours': np.mean(durations_minutes) / 60,
        'avg_holding_days': np.mean(durations_minutes) / (60 * 24),
        'min_holding_minutes': np.min(durations_minutes),
        'max_holding_minutes': np.max(durations_minutes),
        'min_holding_hours': np.min(durations_minutes) / 60,
        'max_holding_hours': np.max(durations_minutes) / 60,
        'median_holding_minutes': np.median(durations_minutes),
        'std_holding_minutes': np.std(durations_minutes)
    }
    
    return stats

def print_holding_analysis(holding_periods, holding_stats):
    """
    打印持仓时间分析结果
    """
    print("\n" + "="*60)
    print("持仓时间分析报告")
    print("="*60)
    
    print(f"\n📊 总体统计:")
    print(f"   完整持仓周期数量: {holding_stats['total_positions']}")
    
    if holding_stats['total_positions'] > 0:
        print(f"   平均持仓时间: {holding_stats['avg_holding_minutes']:.1f} 分钟 ({holding_stats['avg_holding_hours']:.2f} 小时)")
        print(f"   中位数持仓时间: {holding_stats['median_holding_minutes']:.1f} 分钟")
        print(f"   最短持仓时间: {holding_stats['min_holding_minutes']:.1f} 分钟 ({holding_stats['min_holding_hours']:.2f} 小时)")
        print(f"   最长持仓时间: {holding_stats['max_holding_minutes']:.1f} 分钟 ({holding_stats['max_holding_hours']:.2f} 小时)")
        print(f"   持仓时间标准差: {holding_stats['std_holding_minutes']:.1f} 分钟")
        
        # 持仓时间分布分析
        durations = [p['duration_minutes'] for p in holding_periods]
        short_positions = sum(1 for d in durations if d <= 60)  # 1小时以内
        medium_positions = sum(1 for d in durations if 60 < d <= 1440)  # 1小时到1天
        long_positions = sum(1 for d in durations if d > 1440)  # 超过1天
        
        print(f"\n📈 持仓时间分布:")
        print(f"   短期持仓 (≤1小时): {short_positions} 次 ({short_positions/len(durations)*100:.1f}%)")
        print(f"   中期持仓 (1小时-1天): {medium_positions} 次 ({medium_positions/len(durations)*100:.1f}%)")
        print(f"   长期持仓 (>1天): {long_positions} 次 ({long_positions/len(durations)*100:.1f}%)")
        
        print(f"\n📋 详细持仓记录:")
        for i, period in enumerate(holding_periods, 1):
            incomplete_flag = " [未完成]" if period.get('is_incomplete', False) else ""
            print(f"   持仓 {i:2d}: {period['start_time']} → {period['end_time']}")
            print(f"           持续时间: {period['duration_minutes']:6.1f} 分钟 ({period['duration_hours']:5.2f} 小时){incomplete_flag}")
    else:
        print("   ⚠️  未发现完整的持仓周期")
        print("   (可能是因为数据时间范围内没有完整的开仓-平仓周期)")

def analyze_trading_data(res_data):
    """
    完整的交易数据分析流程
    
    Args:
        res_data: 交易结果数据DataFrame，需包含 'time', 'pos' 等列
    """
    print("开始分析交易数据...")
    
    # 确保时间列是datetime类型
    if 'time' in res_data.columns:
        res_data['time'] = pd.to_datetime(res_data['time'])
    
    # 计算持仓周期
    holding_periods = calculate_holding_periods(res_data)
    
    # 计算统计指标
    holding_stats = calculate_holding_statistics(holding_periods)
    
    # 打印分析结果
    print_holding_analysis(holding_periods, holding_stats)
    
    return holding_periods, holding_stats

def plot_holding_period_distribution(holding_periods, save_path=None):
    """
    绘制持仓时间分布图

    Args:
        holding_periods: 持仓周期列表
        save_path: 保存路径，如果为None则显示图表
    """
    if not holding_periods:
        print("没有持仓数据可供绘制")
        return

    # 提取持仓时间（小时）
    durations_hours = [period['duration_hours'] for period in holding_periods]

    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 直方图
    ax1.hist(durations_hours, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('持仓时间 (小时)')
    ax1.set_ylabel('频次')
    ax1.set_title('持仓时间分布直方图')
    ax1.grid(True, alpha=0.3)

    # 箱线图
    ax2.boxplot(durations_hours, vert=True)
    ax2.set_ylabel('持仓时间 (小时)')
    ax2.set_title('持仓时间箱线图')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存至: {save_path}")
    else:
        plt.show()

def create_holding_summary_table(holding_periods):
    """
    创建持仓时间汇总表

    Args:
        holding_periods: 持仓周期列表

    Returns:
        pandas.DataFrame: 汇总表
    """
    if not holding_periods:
        return pd.DataFrame()

    summary_data = []
    for i, period in enumerate(holding_periods, 1):
        summary_data.append({
            '序号': i,
            '开仓时间': period['start_time'],
            '平仓时间': period['end_time'],
            '持仓分钟': round(period['duration_minutes'], 1),
            '持仓小时': round(period['duration_hours'], 2),
            '持仓天数': round(period['duration_days'], 3),
            '状态': '未完成' if period.get('is_incomplete', False) else '已完成'
        })

    return pd.DataFrame(summary_data)

# 示例使用方法
if __name__ == "__main__":
    # 这里是使用示例，实际使用时替换为你的数据
    print("持仓时间分析工具")
    print("使用方法:")
    print("1. 导入模块: from holding_period_analysis import analyze_trading_data")
    print("2. 调用分析: holding_periods, stats = analyze_trading_data(your_res_data)")
    print("3. your_res_data 需要包含 'time' 和 'pos' 列")
    print("4. 绘制分布图: plot_holding_period_distribution(holding_periods)")
    print("5. 创建汇总表: summary_table = create_holding_summary_table(holding_periods)")
