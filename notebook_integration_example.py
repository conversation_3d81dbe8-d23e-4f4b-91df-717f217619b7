#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Notebook集成示例
展示如何在现有的交易分析notebook中集成持仓时间分析功能
"""

import pandas as pd
import numpy as np
from holding_period_analysis import analyze_trading_data, calculate_holding_periods, calculate_holding_statistics

def enhanced_trading_analysis(res):
    """
    增强版交易分析，在原有分析基础上添加持仓时间统计
    
    Args:
        res: 交易结果DataFrame，包含 time, cash, share, close, asset, pos 等列
    """
    
    # ==================== 原有的交易信号分析 ====================
    print("🔍 开始交易信号分析...")
    
    # Identify enter and exit points based on position changes
    res_copy = res.copy()
    res_copy['pos_shift'] = res_copy['pos'].shift(1)
    res_copy['pos_change'] = res_copy['pos'] - res_copy['pos_shift']

    # Define thresholds for enter/exit detection
    enter_threshold = 0.01  # Position increase > 1%
    exit_threshold = -0.01  # Position decrease > 1%

    # Find enter points (position increases significantly)
    enter_points = res_copy[(res_copy['pos_change'] > enter_threshold)]
    # Find exit points (position decreases significantly)
    exit_points = res_copy[(res_copy['pos_change'] < exit_threshold)]

    # ==================== 新增：持仓时间分析 ====================
    print("\n⏱️  开始持仓时间分析...")
    
    # 计算持仓周期
    holding_periods = calculate_holding_periods(res_copy, position_threshold=0.005)
    
    # 计算统计指标
    holding_stats = calculate_holding_statistics(holding_periods)

    # ==================== 综合报告输出 ====================
    print("\n" + "="*80)
    print("📈 综合交易分析报告")
    print("="*80)
    
    # 基础交易统计
    print(f"\n📊 基础交易统计:")
    print(f"   进场信号检测: {len(enter_points)} 次")
    print(f"   出场信号检测: {len(exit_points)} 次")
    
    if not enter_points.empty:
        print(f"   平均进场价格: {enter_points['close'].mean():.4f}")
    if not exit_points.empty:
        print(f"   平均出场价格: {exit_points['close'].mean():.4f}")
    
    print(f"   最终资产价值: {res['asset'].iloc[-1]:,.2f}")
    print(f"   总收益率: {(res['asset'].iloc[-1] / res['asset'].iloc[0] - 1) * 100:.2f}%")

    # 持仓时间统计
    print(f"\n⏱️  持仓时间统计:")
    print(f"   完整持仓周期: {holding_stats['total_positions']} 次")
    
    if holding_stats['total_positions'] > 0:
        print(f"   平均持仓时间: {holding_stats['avg_holding_minutes']:.1f} 分钟 ({holding_stats['avg_holding_hours']:.2f} 小时)")
        print(f"   中位数持仓时间: {holding_stats['median_holding_minutes']:.1f} 分钟")
        print(f"   最短持仓时间: {holding_stats['min_holding_minutes']:.1f} 分钟")
        print(f"   最长持仓时间: {holding_stats['max_holding_minutes']:.1f} 分钟")
        print(f"   持仓时间标准差: {holding_stats['std_holding_minutes']:.1f} 分钟")
        
        # 持仓效率分析
        avg_holding_hours = holding_stats['avg_holding_hours']
        if avg_holding_hours < 1:
            efficiency_comment = "持仓时间较短，属于高频交易模式"
        elif avg_holding_hours < 24:
            efficiency_comment = "持仓时间适中，属于日内交易模式"
        else:
            efficiency_comment = "持仓时间较长，属于中长期交易模式"
        
        print(f"   📝 交易模式评估: {efficiency_comment}")
        
        # 持仓时间分布
        durations = [p['duration_minutes'] for p in holding_periods]
        short_count = sum(1 for d in durations if d <= 60)
        medium_count = sum(1 for d in durations if 60 < d <= 1440)
        long_count = sum(1 for d in durations if d > 1440)
        
        print(f"\n📈 持仓时间分布:")
        print(f"   短期 (≤1小时): {short_count} 次 ({short_count/len(durations)*100:.1f}%)")
        print(f"   中期 (1小时-1天): {medium_count} 次 ({medium_count/len(durations)*100:.1f}%)")
        print(f"   长期 (>1天): {long_count} 次 ({long_count/len(durations)*100:.1f}%)")
        
        # 详细持仓记录（只显示前10个，避免输出过长）
        print(f"\n📋 详细持仓记录 (显示前10个):")
        for i, period in enumerate(holding_periods[:10], 1):
            incomplete_flag = " [未完成]" if period.get('is_incomplete', False) else ""
            print(f"   {i:2d}. {period['start_time']} → {period['end_time']}")
            print(f"       持续: {period['duration_minutes']:6.1f}分钟 ({period['duration_hours']:5.2f}小时){incomplete_flag}")
        
        if len(holding_periods) > 10:
            print(f"   ... 还有 {len(holding_periods) - 10} 个持仓记录")
    else:
        print("   ⚠️  未发现完整的持仓周期")

    # 交易建议
    print(f"\n💡 交易策略建议:")
    if holding_stats['total_positions'] > 0:
        avg_minutes = holding_stats['avg_holding_minutes']
        if avg_minutes < 30:
            print("   - 当前平均持仓时间很短，建议考虑是否过于频繁交易")
            print("   - 可以适当延长持仓时间，减少交易成本")
        elif avg_minutes > 1440:  # 超过1天
            print("   - 当前持仓时间较长，符合中长期投资策略")
            print("   - 建议关注市场趋势变化，及时调整仓位")
        else:
            print("   - 当前持仓时间适中，交易频率合理")
            print("   - 建议继续观察收益率与持仓时间的关系")
    
    print("\n" + "="*80)
    
    return {
        'enter_points': enter_points,
        'exit_points': exit_points,
        'holding_periods': holding_periods,
        'holding_stats': holding_stats,
        'res_copy': res_copy
    }

# 在notebook中的使用示例
def notebook_usage_example():
    """
    在notebook中使用的示例代码
    """
    example_code = '''
# 在你的notebook cell中使用以下代码：

# 1. 导入分析函数
from notebook_integration_example import enhanced_trading_analysis

# 2. 对现有的res数据进行增强分析
analysis_results = enhanced_trading_analysis(res)

# 3. 获取分析结果
enter_points = analysis_results['enter_points']
exit_points = analysis_results['exit_points']
holding_periods = analysis_results['holding_periods']
holding_stats = analysis_results['holding_stats']

# 4. 可以继续使用原有的绘图代码
# 原有的plotly绘图代码保持不变...

# 5. 如果需要单独的持仓时间分析
from holding_period_analysis import analyze_trading_data
periods, stats = analyze_trading_data(res)
'''
    
    print("📝 Notebook使用示例:")
    print(example_code)

if __name__ == "__main__":
    print("📊 增强版交易分析工具")
    print("这个脚本提供了在现有notebook中集成持仓时间分析的完整解决方案")
    print("\n主要功能:")
    print("1. 保持原有的进出场信号分析")
    print("2. 新增持仓时间统计分析")
    print("3. 提供综合交易报告")
    print("4. 给出交易策略建议")
    
    notebook_usage_example()
