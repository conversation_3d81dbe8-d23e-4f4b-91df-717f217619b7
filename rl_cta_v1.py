from elegantrl import train_agent
from elegantrl import Config, get_gym_env_args
# from elegantrl.agents import Agent<PERSON>3<PERSON><PERSON>
# from elegantrl.agents import Agent<PERSON>OV2 as Agent<PERSON><PERSON>
# from elegantrl.agents import <PERSON><PERSON><PERSON>
from elegantrl import envs, agents
# from elegantrl.agents import Agent<PERSON><PERSON><PERSON> as Agent<PERSON><PERSON>
# from elegantrl.agents import Agent<PERSON><PERSON>
# from elegantrl.envs.CTAEnvV1 import CTAEnvV1
# from elegantrl.envs.CTAEnvV2 import CTAEnvV2
# from elegantrl.envs.CTAEnvV3 import CTAEnvV3
# from elegantrl.envs.CTAEnvV4 import CTAEnvV4
# from elegantrl.envs.CTAEnvV4Pos import CTAEnvV4Pos
# from elegantrl.envs.CTAEnvV5 import CTAEnvV5
# from elegantrl.envs.CTAEnvV4Sharpe import CTAEnvV4Sharpe
from copy import deepcopy
from loguru import logger
from utils import *
import fire
import os
import sys

logger.remove()
logger.configure(handlers=[{"sink": sys.stderr, "level": 'INFO'}])

# --horizon_len=512 --action_dim=1 --eval_per_step=2000 --max_step=16 --sample_nums=2048  xxxxx
# 
env_params_ = {
    # data
    "train_start_date": "2021.05.01",
    "valid_start_date": "2021.05.10",
    "test_start_date": "2021.05.20",
    # "train_start_date": "2020.10.01",
    # "valid_start_date": "2022.10.01",
    # "test_start_date": "2023.03.01",
    # exec
    "initial_asset": 1e6,
    "transaction_cost": 2e-4,
    "forward_kl_nums": 15,
    # "max_open_capital": 1e5,
    # "min_open_capital": 2e4,
    "max_holding_capital": 1e6,
    "capital_thresh": 0.3,
    "stop_loss": 0.1,       # TODO
    # model
    "seq_len": 1,
    "fea_freqs": 1,
    # RL --> 0607: 0.9, 考虑10步
    "gamma": 0.95,      # 折扣因子：0.96, 0.98, 0.99, 0.995 --> 56,114,229,459
    "random_reset": True,   # env random reset
    "reward_scale": 2**-10,  # 已在explore中使用，将reward缩放到-1至1附近最佳
    "neg_reward_scale": 1,  # 负reward缩放
    "target_return": 200,   # target
    "max_step": 24*60*7,  # 最大步数
    "eval_max_step": 24*60*14,   # eval max step
    "action_dim": 2,  # 动作维度
    "reward_type": "",
    "encourage_penalty": 0,
    "enable_short": False,  # 是否允许做空
    "feature_norm": False,
    "worker_slice": False,
    "use_reward_scaling": False,
    "use_pos_info": True,
    "reward_base": '',
    "open_thres": 0.0
}

model_params_ = {
    "learning_rate": 1e-4,      # 学习率，通常比监督学习要小,on-policy要略大一些
    "eval_per_step": 1e6,       # 评估步数，step=step()调用次数，根据数据量和任务难易程度确定
    "soft_update_tau": 5e-3,    # 新模型参数的权重，控制网络参数变化速度，在off-policy中使用
    "break_step": 2e7,          # 由数据量和任务难易程度确定
    "eval_times": 1,            # eval抽样次数
    "net_dims": (128, 64), #(8, 8),       # 网络隐藏层dim
    "buffer_size": int(2**18),  # 记忆容量，在off-policy中使用
    "repeat_times": 2,          # 重复更新次数，不要太大，否则新旧策略差异过大
    "batch_size": 512,          # on-policy要略大一些,(512~4096)
    "horizon_len": 24*60*7,    # 轨迹长度
    "if_use_per": False,        # PER采样，一般是off-policy使用
    "per_alpha": 0.1,           # PER参数
    "per_beta": 0.9,            # PER参数
    "explore_rate": 0.1,        # off-policy使用
    "state_value_tau": 0.01,
    "policy_dist": "gaussian",
    "use_state_norm": True,
}

def parse_params(params:dict, default:dict):
    """
    Parse parameters from a dictionary and update the default parameters.
    """
    for key, value in params.items():
        if key in default:
            logger.info(f"Updating parameter {key} from {default[key]} to {value}.")
            default[key] = value
        else:
            logger.warning(f"Parameter {key} not found in default parameters.")
    return default

def run(gpu_id:int=0, drl_id:str='AgentPPO', env_id:str='CTAEnvV4', seed:int=51210, cpu:int=1, cwd:str='', 
        per:bool=False, single:bool=False, tag:str='', symbols: list[str]=[], **kwargs):
    # agent_class = [AgentD3QN, AgentPPO, AgentSAC][drl_id]
    # env_class = [CTAEnvV1, CTAEnvV2, CTAEnvV3, CTAEnvV4, CTAEnvV5, CTAEnvV4Sharpe, CTAEnvV4Pos][env_id]
    agent_class = getattr(agents, drl_id)
    env_class = getattr(envs, env_id)
    env_params = parse_params(kwargs, env_params_)
    model_params = parse_params(kwargs, model_params_)
    if 'D3QN' in drl_id or 'DuelingDQN' in drl_id or 'DQN' in drl_id:
        env_params['if_discrete'] = True
        env_params['if_off_policy'] = True
        model_params['learning_rate'] = 3e-5
    env_params['start_date'] = env_params['train_start_date']
    env_params['end_date'] = env_params['valid_start_date']
    env_params['tag'] = tag
    env_params['symbols'] = symbols
    env_params['transform_st'] = env_params['train_start_date']
    env_params['transform_et'] = env_params['valid_start_date']
    env_params['num_workers'] = cpu
    env = env_class(**env_params)
    env_args = get_gym_env_args(env=env, if_print=False)
    env_args.update(env_params)
    # print(env_args)

    args = Config(agent_class, env_class=env_class, env_args=env_args)
    # args.learning_rate = 2e-5
    args.learning_rate = model_params.get("learning_rate", 2e-5)
    args.eval_per_step = model_params.get("eval_per_step", 4e3)
    args.soft_update_tau = model_params.get("soft_update_tau", 5e-3)
    args.break_step = model_params.get("break_step", 4e6)
    args.eval_times = model_params.get("eval_times", 10)
    args.net_dims = model_params.get("net_dims", (2**10, 2**10))
    args.buffer_size = model_params.get("buffer_size", int(2**20))
    args.repeat_times = model_params.get("repeat_times", 1)
    args.batch_size = model_params.get("batch_size", 512)
    args.horizon_len = model_params.get("horizon_len", 24*60*7)
    args.explore_rate = model_params.get("explore_rate", 0.1)

    # args.horizon_len = env_params['max_step']
    args.state_value_tau = model_params.get("state_value_tau", 0)
    args.policy_dist = model_params.get("policy_dist", "gaussian")

    args.use_state_norm = model_params.get("use_state_norm", True)
    args.norm_nums = getattr(env, 'norm_nums', 0)
    args.gamma = env_params.get("gamma", 0.99)
    args.reward_scale = env_params.get("reward_scale", 1)

    if cwd: args.cwd = cwd
    if per:
        print('use PER!')
        args.if_use_per = True
        args.per_alpha = model_params.get("per_alpha", 0.1)
        args.per_beta = model_params.get("per_beta", 0.1)
    args.gpu_id = gpu_id
    args.learner_gpus = gpu_id
    args.random_seed = seed
    args.num_workers = cpu

    eval_env_params = deepcopy(env_params)
    eval_env = env_class(**eval_env_params)
    eval_env_args = get_gym_env_args(env=eval_env, if_print=False)
    eval_env_args.update(env_params)
    eval_env_args['random_reset'] = False
    # eval_env_args['max_step'] = 30 * 24 * 60 * 6
    eval_env_args['max_step'] = env_params.get("eval_max_step", 14 * 24 * 60)
    eval_env_args['start_date'] = env_params['train_start_date']
    eval_env_args['end_date'] = env_params['valid_start_date']
    eval_env_args['transform_st'] = env_params['train_start_date']
    eval_env_args['transform_et'] = env_params['valid_start_date']
    eval_env_args['start_date'] = env_params['valid_start_date']
    eval_env_args['end_date'] = env_params['test_start_date']
    eval_env_args['num_workers'] = -1
    eval_env_args['eval'] = True
    args.eval_env_args = eval_env_args
    args.print_config()
    train_agent(args, single)


def batch_run(sym_dir:str='', gpu_id:int=0, drl_id:str='', env_id:str='', seed:int=51210, cpu:int=1, test_mode:bool=False, auto_date:bool=False, tag:str='', **kwargs):
    logger.info(f'{kwargs=}')
    sample_nums = kwargs.get('sample_nums', 0)
    start_idx = kwargs.get('start_idx', 0)
    sample_split = kwargs.get('sample_split', 0.8)
    # test_symbols = ['XRP#USDT:USDT', 'DOGE#USDT:USDT', 'SOL#USDT:USDT', 'ADA#USDT:USDT', 'LINK#USDT:USDT'] 
    # test_symbols = ['LTC#USDT:USDT', 'AVAX#USDT:USDT', 'UNI#USDT:USDT', 'DOT#USDT:USDT', 'INJ#USDT:USDT']
    test_symbols = ['LINK#USDT:USDT', 'INJ#USDT:USDT']
    if len(test_symbols) <= 5:
        sym_str = [sym.split('#')[0] for sym in test_symbols]
        sym_str = '-'.join(sym_str)
    else:
        sym_str = f'sym{len(test_symbols)}-{test_symbols[0].split("#")[0]}-{test_symbols[-1].split("#")[0]}'
    if tag:
        sym_str = f'{sym_str}-{tag}'
    logger_path = f"logs/{sym_dir.split('/')[-1]}-{drl_id}-{env_id}-{sym_str}.log"
    # logger.add(logger_path, level='INFO', enqueue=True)
    for sym in os.listdir(sym_dir):
        if test_mode and sym not in test_symbols:
            continue
        sym_tag = sym + '_' + tag if tag else sym
        data_path = f'{sym_dir}/{sym}'
        if auto_date:
            train_start_date, valid_start_date, test_start_date, train_nums, valid_nums = gen_date_params(data_path, split=sample_split, sample_nums=sample_nums, start_idx=start_idx)
            env_params_['train_start_date'] = train_start_date
            env_params_['valid_start_date'] = valid_start_date
            env_params_['test_start_date'] = test_start_date
            # env_params_['max_step'] = int(sample_nums * sample_split)
            # env_params_['max_step'] = int(sample_nums * (1-sample_split) + 24 * 60)
            env_params_['eval_max_step'] = int(sample_nums * (1-sample_split) + 24 * 60)
            logger.info(f'auto date: {sym}, {train_start_date}, {valid_start_date}, {test_start_date}, {train_nums=}, {valid_nums=}')
        else:
            train_start_date = env_params_['train_start_date']
            valid_start_date = env_params_['valid_start_date']
            test_start_date = env_params_['test_start_date']
            logger.info(f'{sym}, {train_start_date}, {valid_start_date}, {test_start_date}')

        env_params_['logger_path'] = logger_path
        run(gpu_id=gpu_id, drl_id=drl_id, env_id=env_id, seed=seed,
            cpu=cpu, single=False, tag=sym_tag, symbols=[sym], **kwargs)


def all_syms_run(sym_dir:str='', gpu_id:int=0, drl_id:str='', env_id:str='', seed:int=51210, cpu:int=1, tag:str='', **kwargs):
    logger.info(f'{kwargs=}')
    sym_str = 'all_syms'
    if tag:
        sym_str = f'{sym_str}-{tag}'
    logger_path = f"logs/{sym_dir.split('/')[-1]}-{drl_id}-{env_id}-{sym_str}.log"
    env_params_['eval_max_step'] = 24*60*90
    env_params_['logger_path'] = logger_path
    env_params_['train_start_date'] = '2023.01.01'
    env_params_['valid_start_date'] = '2024.01.01'
    env_params_['test_start_date'] = '2024.04.01'
    model_params_['eval_times'] = 20
    logger.info(f"{env_params_['train_start_date']}-{env_params_['valid_start_date']}-{env_params_['test_start_date']}")
    # logger.add(logger_path, level='INFO', enqueue=True)
    sym_list = []
    for sym in os.listdir(sym_dir):
        data_path = f'{sym_dir}/{sym}'
        if verify_dataset_between_date(data_path, env_params_['valid_start_date'], env_params_['test_start_date']):
            sym_list.append(sym)
        else:
            logger.info(f'discard {sym} due to date mismatch')

    logger.info(f'train on {len(sym_list)} symbols, {sym_list}')
    sym_tag = f'sym{len(sym_list)}_' + tag if tag else f'sym{len(sym_list)}'

    run(gpu_id=gpu_id, drl_id=drl_id, env_id=env_id, seed=seed,
        cpu=cpu, single=False, tag=sym_tag, symbols=sym_list, **kwargs)


if __name__ == '__main__':
    fire.Fire()

