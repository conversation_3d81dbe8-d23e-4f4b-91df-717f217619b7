python rl_cta_v1.py batch_run --drl_id=1 --env_id=1 --cpu=16 --seed=51210 --sym_dir 

python rl_cta_v1.py batch_run ./data_snap/pred_model_5min_gain_v5_432882_online --drl_id=1 --env_id=3 --cpu=16 --test_mode --learning_rate=0.0001 --reward_scale=0.001 --sample_nums=57600 --auto_date --sample_split=0.75

python rl_cta_v1.py batch_run ./data_snap/pred_model_5min_gain_v5_432882_online --drl_id=1 --env_id=3 --cpu=16 --test_mode --learning_rate=0.0001 --reward_scale=0.001 --sample_nums=525600 --auto_date --sample_split=0.833 --tag=long --eval_max_step=86400 --max_step=432000  

python rl_cta_v1.py batch_run ./data_snap/pred_model_5min_gain_v5_432882_online --drl_id=1 --env_id=3 --cpu=16 --test_mode --learning_rate=0.0001 --reward_scale=0.001 --sample_nums=525600 --auto_date --sample_split=0.833 --tag=long

python rl_cta_v1.py batch_run ./data_snap/pred_model_5min_gain_v5_432882_online --drl_id=1 --env_id=2 --cpu=16 --test_mode --learning_rate=0.0001 --reward_scale=0.001 --sample_nums=525600 --auto_date --sample_split=0.833 --tag=long_fea7 --action_dim=1 --horizon_len=1024 --eval_per_step=100000 --break_step=50000000

python rl_cta_v1.py batch_run ./data_snap/pred_model_5min_gain_v5_432882_online --drl_id=1 --env_id=2 --cpu=16 --test_mode --learning_rate=0.0001 --reward_scale=0.001 --sample_nums=525600 --auto_date --sample_split=0.833 --tag=long_fea7_short_gelu_fix_norm --action_dim=1 --horizon_len=1024 --eval_per_step=100000 --break_step=50000000 --enable_short=True

python rl_cta_v1.py batch_run ./data_snap/pred_model_5min_gain_v5_432882_online --drl_id=1 --env_id=2 --cpu=16 --test_mode --learning_rate=0.0001 --reward_scale=0.001 --sample_nums=525600 --auto_date --sample_split=0.833 --tag=long_fea7_short --action_dim=1 --horizon_len=1024 --eval_per_step=100000 --break_step=50000000 --enable_short=True

# 0719
python rl_cta_v1.py batch_run ./data_snap/pred_model_5min_gain_v5_432882_online --drl_id=AgentPPOMax --env_id=CTAEnvV3 --cpu=8 --test_mode --sample_nums=525600 --auto_date --sample_split=0.833 --tag=long_fea7_noslice_snorm_sharpe --action_dim=1 --open_thres=0.0 --reward_type=sharpe --reward_scale=100

# gpu5
python rl_cta_v1.py batch_run ./data_snap/pred_model_5min_gain_v5_432882_online --drl_id=AgentD3QN --env_id=FusionEnvV1Discrete --cpu=8 --test_mode --sample_nums=525600 --auto_date --sample_split=0.833 --tag=long_fea7_noslice_snorm_sharpe --action_dim=3 --open_thres=0.0 --reward_type=sharpe --reward_scale=100

# all symbol 
python rl_cta_v1.py all_syms_run ./data_snap/pred_model_5min_gain_v5_432882_online --drl_id=AgentPPOMax --env_id=CTAEnvV3 --cpu=8 --tag=v5_all --action_dim=1 --reward_type=sharpe --reward_scale=100