#!/usr/bin/env python3
"""
CTAEnvV3 测试脚本
用于验证优化后的环境是否正常工作
"""

import numpy as np
import pandas as pd
import os
import sys
from loguru import logger

# 添加项目路径
sys.path.append('.')

def test_ctaenv_v3_basic():
    """基础功能测试"""
    try:
        from elegantrl.envs.CTAEnvV3 import CTAEnvV3
        
        # 创建环境实例
        env_config = {
            'symbols': ['DOGE#USDT:USDT'],
            'start_date': '2023.01.01',
            'end_date': '2023.02.01',
            'initial_asset': 100000,
            'transaction_cost': 3e-4,
            'enable_short': True,
            'reward_type': 'absolute',
            'min_action': 0.05,
            'eval': True
        }
        
        env = CTAEnvV3(**env_config)
        logger.info("✓ 环境创建成功")
        
        # 测试重置
        state, info = env.reset()
        logger.info(f"✓ 环境重置成功，状态维度: {state.shape}")
        
        # 测试状态验证
        is_valid = env.validate_state()
        logger.info(f"✓ 状态验证: {'通过' if is_valid else '失败'}")
        
        return env
        
    except Exception as e:
        logger.error(f"✗ 基础功能测试失败: {e}")
        return None

def test_trading_scenarios(env):
    """测试不同交易场景"""
    if env is None:
        return False
    
    try:
        # 测试做多
        logger.info("测试做多场景...")
        action = np.array([0.5])  # 50%仓位做多
        state, reward, done, truncated, info = env.step(action)
        logger.info(f"做多 - 奖励: {reward:.4f}, 总资产: {env.total_asset:.2f}")
        
        # 测试减仓
        logger.info("测试减仓场景...")
        action = np.array([0.2])  # 减仓到20%
        state, reward, done, truncated, info = env.step(action)
        logger.info(f"减仓 - 奖励: {reward:.4f}, 总资产: {env.total_asset:.2f}")
        
        # 测试做空（如果启用）
        if env.enable_short:
            logger.info("测试做空场景...")
            action = np.array([-0.3])  # 30%仓位做空
            state, reward, done, truncated, info = env.step(action)
            logger.info(f"做空 - 奖励: {reward:.4f}, 总资产: {env.total_asset:.2f}")
        
        # 测试平仓
        logger.info("测试平仓场景...")
        action = np.array([0.0])  # 平仓
        state, reward, done, truncated, info = env.step(action)
        logger.info(f"平仓 - 奖励: {reward:.4f}, 总资产: {env.total_asset:.2f}")
        
        logger.info("✓ 交易场景测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 交易场景测试失败: {e}")
        return False

def test_portfolio_stats(env):
    """测试投资组合统计功能"""
    if env is None:
        return False
    
    try:
        # 执行一些随机交易
        for i in range(10):
            action = np.random.uniform(-0.5, 0.5, 1) if env.enable_short else np.random.uniform(0, 1, 1)
            state, reward, done, truncated, info = env.step(action)
            if done:
                break
        
        # 获取统计信息
        stats = env.get_portfolio_stats()
        logger.info("投资组合统计信息:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value:.4f}")
        
        logger.info("✓ 投资组合统计测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 投资组合统计测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    try:
        from elegantrl.envs.CTAEnvV3 import CTAEnvV3
        
        # 测试无效动作处理
        env_config = {
            'symbols': ['DOGE#USDT:USDT'],
            'start_date': '2023.01.01',
            'end_date': '2023.02.01',
            'initial_asset': 100000,
            'enable_short': False
        }
        
        env = CTAEnvV3(**env_config)
        state, info = env.reset()
        
        # 测试无效动作
        invalid_actions = [
            np.array([np.inf]),
            np.array([np.nan]),
            np.array([10.0]),  # 超出范围
            np.array([-5.0])   # 超出范围
        ]
        
        for i, action in enumerate(invalid_actions):
            try:
                normalized_action = env._norm_action(action)
                logger.info(f"无效动作 {i+1}: {action[0]} -> {normalized_action}")
            except Exception as e:
                logger.warning(f"动作 {i+1} 处理异常: {e}")
        
        logger.info("✓ 错误处理测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始CTAEnvV3优化验证测试...")
    
    # 基础功能测试
    env = test_ctaenv_v3_basic()
    
    # 交易场景测试
    test_trading_scenarios(env)
    
    # 投资组合统计测试
    test_portfolio_stats(env)
    
    # 错误处理测试
    test_error_handling()
    
    logger.info("CTAEnvV3测试完成!")

if __name__ == "__main__":
    main()
