#!/usr/bin/env python3
"""
Holding Time Optimization Validation Script
===========================================

This script validates the optimizations made to CTAEnvV5 to encourage longer position holding times.
It tests the new features and measures their effectiveness in reducing trading frequency.
"""

import numpy as np
import torch as th
import sys
import os
from typing import Dict, List

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from elegantrl.envs.CTAEnvV5 import CTAEnvV5
    from elegantrl.agents.AgentSACOpt import AgentSACOpt
    from elegantrl.train import Config
    print("✓ Successfully imported optimized components")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


class HoldingTimeValidator:
    """Validator for holding time optimization features"""
    
    def __init__(self):
        self.test_results = {}
    
    def test_parameter_optimization(self):
        """Test that optimized parameters are correctly set"""
        print("\n=== Testing Parameter Optimization ===")
        
        env = CTAEnvV5(
            symbols=[],
            initial_asset=100000,
            eval=False
        )
        
        # Test transaction cost increases
        assert env.maker_fee == 0.002, f"Expected maker_fee=0.002, got {env.maker_fee}"
        assert env.taker_fee == 0.003, f"Expected taker_fee=0.003, got {env.taker_fee}"
        assert env.slippage_impact == 0.001, f"Expected slippage_impact=0.001, got {env.slippage_impact}"
        print("✓ Transaction costs increased (10x from original)")
        
        # Test reward function weights
        assert env.turnover_penalty == 3.0, f"Expected turnover_penalty=3.0, got {env.turnover_penalty}"
        assert env.consistency_bonus == 0.5, f"Expected consistency_bonus=0.5, got {env.consistency_bonus}"
        print("✓ Reward function weights optimized for longer holding")
        
        # Test new holding time parameters
        assert hasattr(env, 'holding_time_bonus'), "Missing holding_time_bonus parameter"
        assert hasattr(env, 'min_holding_periods'), "Missing min_holding_periods parameter"
        assert hasattr(env, 'trading_frequency_penalty'), "Missing trading_frequency_penalty parameter"
        print("✓ New holding time parameters present")
        
        # Test minimum action threshold
        assert env.min_action == 0.15, f"Expected min_action=0.15, got {env.min_action}"
        print("✓ Minimum action threshold increased (3x from 0.05 to 0.15)")
        
        return True
    
    def test_holding_time_tracking(self):
        """Test holding time tracking functionality"""
        print("\n=== Testing Holding Time Tracking ===")
        
        env = CTAEnvV5(
            symbols=[],
            initial_asset=100000,
            eval=False
        )
        
        # Test initial state
        assert env.position_holding_time == 0, "Initial holding time should be 0"
        assert env.consecutive_holds == 0, "Initial consecutive holds should be 0"
        print("✓ Initial holding time tracking state correct")
        
        # Simulate holding behavior (no position changes)
        mock_actions = [0.1, 0.1, 0.1, 0.1, 0.1]  # Small actions below min_action threshold
        
        for i, action in enumerate(mock_actions):
            # Mock the necessary attributes for testing
            env.cur_action = action
            env.shares = 1000
            env.cur_close = 100
            env.total_asset = 100000
            env.next_close = 101
            
            # Test holding time increment logic
            prev_pos = env.shares * env.cur_close / env.total_asset
            diff_pos = action - prev_pos
            
            if abs(diff_pos) <= env.min_action:
                env.position_holding_time += 1
                env.consecutive_holds += 1
                env.trading_frequency_history.append(0)
        
        assert env.position_holding_time == 5, f"Expected holding_time=5, got {env.position_holding_time}"
        assert env.consecutive_holds == 5, f"Expected consecutive_holds=5, got {env.consecutive_holds}"
        print("✓ Holding time tracking works correctly")
        
        return True
    
    def test_frequency_penalties(self):
        """Test frequency-based penalty calculations"""
        print("\n=== Testing Frequency Penalties ===")
        
        env = CTAEnvV5(
            symbols=[],
            initial_asset=100000,
            eval=False
        )
        
        # Test frequency penalty calculation
        env.trading_frequency_history = [1] * 10 + [0] * 10  # 50% trading frequency
        frequency_penalty = env._calculate_frequency_penalty()
        assert frequency_penalty > 0, "Should have penalty for high frequency trading"
        print(f"✓ Frequency penalty calculated: {frequency_penalty:.4f}")
        
        # Test holding time penalty
        env.position_holding_time = 2  # Less than min_holding_periods (5)
        holding_penalty = env._calculate_holding_time_penalty()
        assert holding_penalty > 0, "Should have penalty for short holding time"
        print(f"✓ Holding time penalty calculated: {holding_penalty:.4f}")
        
        # Test no penalty for long holding
        env.position_holding_time = 10  # More than min_holding_periods
        holding_penalty = env._calculate_holding_time_penalty()
        assert holding_penalty == 0, "Should have no penalty for long holding time"
        print("✓ No penalty for sufficient holding time")
        
        return True
    
    def test_reward_components(self):
        """Test new reward components"""
        print("\n=== Testing New Reward Components ===")
        
        env = CTAEnvV5(
            symbols=[],
            initial_asset=100000,
            eval=False
        )
        
        # Setup for reward calculation
        env.position_holding_time = 10  # Long holding time
        env.consecutive_holds = 8  # Good momentum
        env.returns_history = [0.01] * 20  # Some return history
        
        # Test reward components calculation
        components = env._calculate_reward_components(base_reward=100.0, transaction_cost=5.0)
        
        # Check new components exist
        assert 'holding_time_bonus' in components, "Missing holding_time_bonus component"
        assert 'position_momentum_bonus' in components, "Missing position_momentum_bonus component"
        
        # Check bonuses are positive for good behavior
        assert components['holding_time_bonus'] > 0, "Should have positive holding time bonus"
        assert components['position_momentum_bonus'] > 0, "Should have positive momentum bonus"
        
        print(f"✓ Holding time bonus: {components['holding_time_bonus']:.4f}")
        print(f"✓ Position momentum bonus: {components['position_momentum_bonus']:.4f}")
        
        return True
    
    def test_enhanced_state_representation(self):
        """Test enhanced state representation with holding time features"""
        print("\n=== Testing Enhanced State Representation ===")
        
        env = CTAEnvV5(
            symbols=[],
            initial_asset=100000,
            eval=False
        )
        
        # Setup holding time tracking
        env.position_holding_time = 7
        env.last_position_change_time = 3
        env.consecutive_holds = 5
        env.trading_frequency_history = [0, 1, 0, 0, 1] * 4
        env.returns_history = [0.01, -0.005, 0.02] * 10
        
        # Test risk management features
        risk_features = env._get_risk_management_features()
        
        # Should have 10 features now (5 original + 5 new)
        expected_features = 10
        assert len(risk_features) == expected_features, f"Expected {expected_features} features, got {len(risk_features)}"
        print(f"✓ Enhanced state has {len(risk_features)} risk management features")
        
        # Test regime stability calculation
        stability = env._calculate_regime_stability()
        assert 0.0 <= stability <= 1.0, f"Stability should be in [0,1], got {stability}"
        print(f"✓ Regime stability calculated: {stability:.4f}")
        
        return True
    
    def test_transaction_cost_scaling(self):
        """Test enhanced transaction cost scaling"""
        print("\n=== Testing Transaction Cost Scaling ===")
        
        env = CTAEnvV5(
            symbols=[],
            initial_asset=100000,
            eval=False
        )
        
        # Setup for cost calculation
        env.total_asset = 100000
        env.trading_frequency_history = [1] * 15  # High frequency
        env.position_holding_time = 1  # Short holding time
        
        # Test cost calculation with penalties
        cost_with_penalties = env.calculate_enhanced_transaction_costs(0.2, 1.0)
        
        # Reset to low frequency scenario
        env.trading_frequency_history = [0] * 15  # Low frequency
        env.position_holding_time = 10  # Long holding time
        
        cost_without_penalties = env.calculate_enhanced_transaction_costs(0.2, 1.0)
        
        assert cost_with_penalties > cost_without_penalties, "High frequency should have higher costs"
        print(f"✓ High frequency cost: {cost_with_penalties:.2f}")
        print(f"✓ Low frequency cost: {cost_without_penalties:.2f}")
        print(f"✓ Cost ratio: {cost_with_penalties/cost_without_penalties:.2f}x")
        
        return True
    
    def run_all_tests(self):
        """Run all validation tests"""
        print("Holding Time Optimization Validation")
        print("=" * 50)
        
        tests = [
            ("Parameter Optimization", self.test_parameter_optimization),
            ("Holding Time Tracking", self.test_holding_time_tracking),
            ("Frequency Penalties", self.test_frequency_penalties),
            ("Reward Components", self.test_reward_components),
            ("Enhanced State Representation", self.test_enhanced_state_representation),
            ("Transaction Cost Scaling", self.test_transaction_cost_scaling)
        ]
        
        passed = 0
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name}...")
            try:
                result = test_func()
                if result:
                    passed += 1
                    print(f"✅ {test_name} PASSED")
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"❌ {test_name} FAILED with exception: {e}")
        
        print("\n" + "=" * 50)
        print("VALIDATION SUMMARY")
        print("=" * 50)
        print(f"Tests passed: {passed}/{len(tests)}")
        
        if passed == len(tests):
            print("\n🎉 All holding time optimizations validated successfully!")
            print("\nKey Improvements:")
            print("• 3x higher minimum action threshold (0.05 → 0.15)")
            print("• 30x higher turnover penalty (0.1 → 3.0)")
            print("• 10x higher transaction costs")
            print("• New holding time bonuses and momentum rewards")
            print("• Enhanced state representation with stability indicators")
            print("• Frequency-based penalty system")
            return True
        else:
            print(f"\n⚠️ {len(tests) - passed} test(s) failed. Review the issues above.")
            return False


def main():
    """Run holding time optimization validation"""
    validator = HoldingTimeValidator()
    success = validator.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
