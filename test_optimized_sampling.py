#!/usr/bin/env python3
"""
Test script for optimized sampling efficiency in long-only trading scenarios

This script validates the improvements made to CTAEnvV5 for better handling
of sparse positive samples in long-only trading environments.
"""

import numpy as np
import pandas as pd
import time
from loguru import logger
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

# Import the optimized environment
from elegantrl.envs.CTAEnvV5 import CTAEnvV5
from elegantrl.envs.CTAEnvV3 import CTAEnvV3


class SamplingEfficiencyTester:
    """Test class to compare sampling efficiency between original and optimized environments"""
    
    def __init__(self, symbols: List[str] = None):
        """
        Initialize the tester
        
        Args:
            symbols: List of symbols to test with
        """
        self.symbols = symbols or ['DOGE#USDT:USDT', 'XRP#USDT:USDT', 'SOL#USDT:USDT']
        self.results = {
            'original': {},
            'optimized': {}
        }
    
    def test_episode_generation_speed(self, num_episodes: int = 50) -> Dict[str, float]:
        """
        Test the speed of episode generation for both environments
        
        Args:
            num_episodes: Number of episodes to test
            
        Returns:
            Dictionary with timing results
        """
        logger.info(f"Testing episode generation speed with {num_episodes} episodes")
        
        # Test original environment (CTAEnvV3)
        logger.info("Testing original environment (CTAEnvV3)...")
        original_env = CTAEnvV3(
            symbols=self.symbols,
            initial_asset=100000,
            enable_short=False,  # Long-only
            random_reset=True
        )
        
        start_time = time.time()
        original_positive_samples = []
        original_total_samples = []
        
        for i in range(num_episodes):
            try:
                original_env.reset()
                if hasattr(original_env, 'data') and 'score' in original_env.data.columns:
                    score_threshold = getattr(original_env, 'score_thres', 0.4)
                    positive_count = np.sum(original_env.data['score'] > score_threshold)
                    total_count = len(original_env.data)
                    original_positive_samples.append(positive_count)
                    original_total_samples.append(total_count)
            except Exception as e:
                logger.warning(f"Original env episode {i} failed: {e}")
        
        original_time = time.time() - start_time
        
        # Test optimized environment (CTAEnvV5)
        logger.info("Testing optimized environment (CTAEnvV5)...")
        optimized_env = CTAEnvV5(
            symbols=self.symbols,
            initial_asset=100000,
            enable_short=False,  # Long-only
            enable_adaptive_sampling=True,
            min_positive_samples=3,
            sample_balance_ratio=0.3,
            random_reset=True
        )
        
        start_time = time.time()
        optimized_positive_samples = []
        optimized_total_samples = []
        
        for i in range(num_episodes):
            try:
                optimized_env.reset()
                if hasattr(optimized_env, 'episode_positive_samples'):
                    optimized_positive_samples.append(optimized_env.episode_positive_samples)
                    optimized_total_samples.append(optimized_env.episode_total_samples)
            except Exception as e:
                logger.warning(f"Optimized env episode {i} failed: {e}")
        
        optimized_time = time.time() - start_time
        
        # Calculate statistics
        results = {
            'original_time': original_time,
            'optimized_time': optimized_time,
            'speedup': original_time / optimized_time if optimized_time > 0 else float('inf'),
            'original_avg_positive': np.mean(original_positive_samples) if original_positive_samples else 0,
            'optimized_avg_positive': np.mean(optimized_positive_samples) if optimized_positive_samples else 0,
            'original_avg_total': np.mean(original_total_samples) if original_total_samples else 0,
            'optimized_avg_total': np.mean(optimized_total_samples) if optimized_total_samples else 0,
            'original_positive_ratio': np.mean(original_positive_samples) / np.mean(original_total_samples) if original_total_samples and np.mean(original_total_samples) > 0 else 0,
            'optimized_positive_ratio': np.mean(optimized_positive_samples) / np.mean(optimized_total_samples) if optimized_total_samples and np.mean(optimized_total_samples) > 0 else 0
        }
        
        self.results['timing'] = results
        return results
    
    def test_reward_signal_quality(self, num_steps: int = 1000) -> Dict[str, float]:
        """
        Test the quality of reward signals in sparse positive sample scenarios
        
        Args:
            num_steps: Number of steps to test
            
        Returns:
            Dictionary with reward signal quality metrics
        """
        logger.info(f"Testing reward signal quality with {num_steps} steps")
        
        # Test optimized environment with sparse reward optimization
        optimized_env = CTAEnvV5(
            symbols=self.symbols,
            initial_asset=100000,
            enable_short=False,
            enable_adaptive_sampling=True,
            min_positive_samples=2,  # Very sparse
            sample_balance_ratio=0.15,  # Low target ratio
            random_reset=True
        )
        
        optimized_env.reset()
        optimized_rewards = []
        optimized_actions = []
        optimized_scores = []
        
        for step in range(min(num_steps, optimized_env.max_step)):
            # Random action for testing
            action = np.random.uniform(0, 1, 1)
            
            try:
                state, reward, done, truncated, info = optimized_env.step(action)
                optimized_rewards.append(reward)
                optimized_actions.append(optimized_env.cur_action)
                
                if hasattr(optimized_env, 'raw_state') and len(optimized_env.raw_state) > 0:
                    optimized_scores.append(optimized_env.raw_state[0])
                
                if done:
                    break
            except Exception as e:
                logger.warning(f"Step {step} failed: {e}")
                break
        
        # Calculate reward signal quality metrics
        results = {
            'avg_reward': np.mean(optimized_rewards) if optimized_rewards else 0,
            'reward_std': np.std(optimized_rewards) if optimized_rewards else 0,
            'positive_rewards': np.sum(np.array(optimized_rewards) > 0) if optimized_rewards else 0,
            'negative_rewards': np.sum(np.array(optimized_rewards) < 0) if optimized_rewards else 0,
            'zero_rewards': np.sum(np.array(optimized_rewards) == 0) if optimized_rewards else 0,
            'total_steps': len(optimized_rewards),
            'avg_action': np.mean(optimized_actions) if optimized_actions else 0,
            'action_diversity': np.std(optimized_actions) if optimized_actions else 0,
            'avg_score': np.mean(optimized_scores) if optimized_scores else 0
        }
        
        self.results['reward_quality'] = results
        return results
    
    def generate_report(self) -> str:
        """
        Generate a comprehensive report of the testing results
        
        Returns:
            Formatted report string
        """
        report = "\n" + "="*80 + "\n"
        report += "OPTIMIZED SAMPLING EFFICIENCY TEST REPORT\n"
        report += "="*80 + "\n\n"
        
        # Timing results
        if 'timing' in self.results:
            timing = self.results['timing']
            report += "EPISODE GENERATION SPEED:\n"
            report += f"  Original Environment Time: {timing['original_time']:.2f}s\n"
            report += f"  Optimized Environment Time: {timing['optimized_time']:.2f}s\n"
            report += f"  Speed Improvement: {timing['speedup']:.2f}x\n\n"
            
            report += "POSITIVE SAMPLE STATISTICS:\n"
            report += f"  Original Avg Positive Samples: {timing['original_avg_positive']:.1f}\n"
            report += f"  Optimized Avg Positive Samples: {timing['optimized_avg_positive']:.1f}\n"
            report += f"  Original Positive Ratio: {timing['original_positive_ratio']:.3f}\n"
            report += f"  Optimized Positive Ratio: {timing['optimized_positive_ratio']:.3f}\n\n"
        
        # Reward quality results
        if 'reward_quality' in self.results:
            quality = self.results['reward_quality']
            report += "REWARD SIGNAL QUALITY:\n"
            report += f"  Average Reward: {quality['avg_reward']:.4f}\n"
            report += f"  Reward Standard Deviation: {quality['reward_std']:.4f}\n"
            report += f"  Positive Rewards: {quality['positive_rewards']}/{quality['total_steps']}\n"
            report += f"  Negative Rewards: {quality['negative_rewards']}/{quality['total_steps']}\n"
            report += f"  Zero Rewards: {quality['zero_rewards']}/{quality['total_steps']}\n"
            report += f"  Average Action: {quality['avg_action']:.3f}\n"
            report += f"  Action Diversity (std): {quality['action_diversity']:.3f}\n"
            report += f"  Average Score: {quality['avg_score']:.3f}\n\n"
        
        report += "OPTIMIZATION SUMMARY:\n"
        report += "✓ Adaptive symbol selection based on positive sample density\n"
        report += "✓ Reduced minimum positive sample requirement (10 → 3)\n"
        report += "✓ Positive sample augmentation for better balance\n"
        report += "✓ Sparse reward optimization with exploration bonuses\n"
        report += "✓ Potential-based reward shaping for intermediate signals\n\n"
        
        report += "="*80 + "\n"
        
        return report


def main():
    """Main function to run the sampling efficiency tests"""
    logger.info("Starting optimized sampling efficiency tests...")
    
    # Initialize tester
    tester = SamplingEfficiencyTester()
    
    # Run tests
    try:
        logger.info("Running episode generation speed test...")
        timing_results = tester.test_episode_generation_speed(num_episodes=20)
        
        logger.info("Running reward signal quality test...")
        quality_results = tester.test_reward_signal_quality(num_steps=500)
        
        # Generate and print report
        report = tester.generate_report()
        print(report)
        
        # Save results
        logger.info("Saving test results...")
        with open('sampling_efficiency_test_results.txt', 'w') as f:
            f.write(report)
        
        logger.info("Tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise


if __name__ == "__main__":
    main()
