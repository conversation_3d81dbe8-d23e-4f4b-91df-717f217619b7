#!/usr/bin/env python3
"""
手续费计算测试脚本
验证CTAEnvV3中手续费计算的准确性
"""

import numpy as np
import sys
from loguru import logger

# 添加项目路径
sys.path.append('.')

def test_transaction_cost_calculation():
    """测试手续费计算的准确性"""
    try:
        from elegantrl.envs.CTAEnvV3 import CTAEnvV3
        
        # 创建测试环境
        env_config = {
            'symbols': ['DOGE#USDT:USDT'],
            'start_date': '2023.01.01',
            'end_date': '2023.02.01',
            'initial_asset': 100000,
            'transaction_cost': 0.001,  # 0.1% 手续费
            'enable_short': True,
            'reward_type': 'absolute',
            'min_action': 0.01,
            'eval': True
        }
        
        env = CTAEnvV3(**env_config)
        state, info = env.reset()
        
        logger.info("=== 手续费计算测试开始 ===")
        logger.info(f"初始状态: 现金={env.cash:.2f}, 股票={env.shares:.4f}, 总资产={env.total_asset:.2f}")
        logger.info(f"当前价格: {env.cur_close:.4f}")
        
        # 测试场景1: 买入操作
        logger.info("\n--- 测试场景1: 买入50%仓位 ---")
        initial_cash = env.cash
        initial_shares = env.shares
        initial_total = env.total_asset
        
        action = np.array([0.5])  # 50%仓位
        state, reward, done, truncated, info = env.step(action)
        
        # 计算预期值
        target_value = 0.5 * initial_total
        expected_shares = target_value / env.cur_close
        expected_cost = expected_shares * env.cur_close
        expected_fee = expected_cost * env.transaction_cost
        expected_cash = initial_cash - expected_cost - expected_fee
        
        logger.info(f"预期: 股票={expected_shares:.4f}, 现金={expected_cash:.2f}, 手续费={expected_fee:.4f}")
        logger.info(f"实际: 股票={env.shares:.4f}, 现金={env.cash:.2f}")
        logger.info(f"资产变化: {env.total_asset - initial_total:.4f}")
        
        # 验证资产平衡
        calculated_total = env.cash + env.shares * env.cur_close
        logger.info(f"资产平衡检查: 计算={calculated_total:.2f}, 记录={env.total_asset:.2f}")
        
        # 测试场景2: 卖出操作
        logger.info("\n--- 测试场景2: 减仓到20% ---")
        pre_cash = env.cash
        pre_shares = env.shares
        pre_total = env.total_asset
        
        action = np.array([0.2])  # 减仓到20%
        state, reward, done, truncated, info = env.step(action)
        
        logger.info(f"减仓前: 股票={pre_shares:.4f}, 现金={pre_cash:.2f}")
        logger.info(f"减仓后: 股票={env.shares:.4f}, 现金={env.cash:.2f}")
        logger.info(f"资产变化: {env.total_asset - pre_total:.4f}")
        
        # 测试场景3: 做空操作（如果启用）
        if env.enable_short:
            logger.info("\n--- 测试场景3: 做空30% ---")
            pre_cash = env.cash
            pre_shares = env.shares
            pre_total = env.total_asset
            
            action = np.array([-0.3])  # 30%做空
            state, reward, done, truncated, info = env.step(action)
            
            logger.info(f"做空前: 股票={pre_shares:.4f}, 现金={pre_cash:.2f}")
            logger.info(f"做空后: 股票={env.shares:.4f}, 现金={env.cash:.2f}")
            logger.info(f"资产变化: {env.total_asset - pre_total:.4f}")
        
        # 测试场景4: 平仓操作
        logger.info("\n--- 测试场景4: 完全平仓 ---")
        pre_cash = env.cash
        pre_shares = env.shares
        pre_total = env.total_asset
        
        action = np.array([0.0])  # 平仓
        state, reward, done, truncated, info = env.step(action)
        
        logger.info(f"平仓前: 股票={pre_shares:.4f}, 现金={pre_cash:.2f}")
        logger.info(f"平仓后: 股票={env.shares:.4f}, 现金={env.cash:.2f}")
        logger.info(f"资产变化: {env.total_asset - pre_total:.4f}")
        
        logger.info("\n=== 手续费计算测试完成 ===")
        return True
        
    except Exception as e:
        logger.error(f"手续费测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    try:
        from elegantrl.envs.CTAEnvV3 import CTAEnvV3
        
        logger.info("\n=== 边界情况测试 ===")
        
        # 创建资金较少的环境
        env_config = {
            'symbols': ['DOGE#USDT:USDT'],
            'start_date': '2023.01.01',
            'end_date': '2023.02.01',
            'initial_asset': 1000,  # 较少的初始资金
            'transaction_cost': 0.01,  # 较高的手续费
            'enable_short': False,
            'min_action': 0.01,
            'eval': True
        }
        
        env = CTAEnvV3(**env_config)
        state, info = env.reset()
        
        logger.info(f"初始状态: 现金={env.cash:.2f}, 价格={env.cur_close:.4f}")
        
        # 测试资金不足的情况
        logger.info("\n--- 测试资金不足情况 ---")
        action = np.array([0.99])  # 尝试买入99%
        state, reward, done, truncated, info = env.step(action)
        
        logger.info(f"尝试大额买入后: 股票={env.shares:.4f}, 现金={env.cash:.2f}")
        
        # 测试卖出超过持有量的情况
        logger.info("\n--- 测试卖出超量情况 ---")
        current_shares = env.shares
        action = np.array([0.0])  # 尝试全部卖出
        state, reward, done, truncated, info = env.step(action)
        
        logger.info(f"全部卖出后: 股票={env.shares:.4f}, 现金={env.cash:.2f}")
        
        logger.info("=== 边界情况测试完成 ===")
        return True
        
    except Exception as e:
        logger.error(f"边界情况测试失败: {e}")
        return False

def test_asset_balance():
    """测试资产平衡"""
    try:
        from elegantrl.envs.CTAEnvV3 import CTAEnvV3
        
        logger.info("\n=== 资产平衡测试 ===")
        
        env_config = {
            'symbols': ['DOGE#USDT:USDT'],
            'start_date': '2023.01.01',
            'end_date': '2023.02.01',
            'initial_asset': 50000,
            'transaction_cost': 0.0005,
            'enable_short': True,
            'min_action': 0.01,
            'eval': True
        }
        
        env = CTAEnvV3(**env_config)
        state, info = env.reset()
        
        initial_total = env.total_asset
        logger.info(f"初始总资产: {initial_total:.2f}")
        
        # 执行多次随机交易
        for i in range(5):
            action = np.random.uniform(-0.5, 0.5, 1) if env.enable_short else np.random.uniform(0, 1, 1)
            state, reward, done, truncated, info = env.step(action)
            
            # 检查资产平衡
            calculated_total = env.cash + env.shares * env.cur_close
            balance_error = abs(calculated_total - env.total_asset)
            
            logger.info(f"交易{i+1}: 动作={action[0]:.3f}, "
                       f"股票={env.shares:.4f}, 现金={env.cash:.2f}, "
                       f"总资产={env.total_asset:.2f}, 平衡误差={balance_error:.6f}")
            
            if balance_error > 1e-3:  # 误差超过阈值
                logger.warning(f"资产平衡误差过大: {balance_error:.6f}")
            
            if done:
                break
        
        logger.info("=== 资产平衡测试完成 ===")
        return True
        
    except Exception as e:
        logger.error(f"资产平衡测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始手续费计算验证测试...")
    
    success_count = 0
    total_tests = 3
    
    # 基础手续费计算测试
    if test_transaction_cost_calculation():
        success_count += 1
    
    # 边界情况测试
    if test_edge_cases():
        success_count += 1
    
    # 资产平衡测试
    if test_asset_balance():
        success_count += 1
    
    logger.info(f"\n测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.info("✓ 所有手续费计算测试通过!")
    else:
        logger.warning("✗ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
