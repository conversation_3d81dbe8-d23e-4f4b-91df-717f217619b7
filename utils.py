import pandas as pd

def gen_date_params(data_path: str, split:float=0.8, sample_nums=0, start_idx=0):
    data = pd.read_parquet(data_path)
    data = data.drop_duplicates(subset=['datatime', 'symbol'])
    data = data.set_index('datatime').sort_index()
    date_index = data.index
    if sample_nums:
        date_index = date_index[start_idx:start_idx+sample_nums]

    train_start_date = date_index[0].strftime('%Y.%m.%d')
    valid_start_date = date_index[int(len(date_index) * split)].strftime('%Y.%m.%d')
    test_start_date = date_index[-1].strftime('%Y.%m.%d')
    return train_start_date, valid_start_date, test_start_date, int(len(date_index) * split), len(date_index)-int(len(date_index) * split)
    


def verify_dataset_between_date(data_path: str, start_date: str, end_date: str):
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    data = pd.read_parquet(data_path)
    data = data.drop_duplicates(subset=['datatime', 'symbol'])
    data = data.set_index('datatime').sort_index()
    date_index = data.index
    if date_index[0] <= start_date and date_index[-1] >= end_date:
        return True
    return False